<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AWSL One API 管理面板</title>
    <link rel="icon"
        href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🤖</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .header.compact {
            padding: 15px 20px;
        }

        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .header.compact h1 {
            font-size: 1.4rem;
            margin-bottom: 5px;
        }

        .header p {
            margin-bottom: 0;
        }

        .header.compact p {
            font-size: 0.9rem;
        }

        .header-info {
            position: absolute;
            top: 15px;
            left: 20px;
            font-size: 12px;
            opacity: 0.8;
        }

        .header-info a {
            color: white;
            text-decoration: none;
            margin-right: 15px;
        }

        .header-info a:hover {
            text-decoration: underline;
        }

        .auth-section {
            padding: 60px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            text-align: center;
        }

        .auth-container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .auth-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 30px;
            color: #2d3748;
        }

        .auth-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .auth-input:focus {
            outline: none;
            border-color: #4c51bf;
            box-shadow: 0 0 0 3px rgba(76, 81, 191, 0.1);
        }

        .auth-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .auth-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(76, 81, 191, 0.3);
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .tab {
            flex: 1;
            padding: 12px 16px;
            text-align: center;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 14px;
            transition: all 0.3s;
        }

        .tab.active {
            background: white;
            border-bottom: 3px solid #4c51bf;
            color: #4c51bf;
            font-weight: 600;
        }

        .tab:hover {
            background: #e9ecef;
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .btn {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
            color: white;
            border: 2px solid transparent;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
            margin: 4px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 81, 191, 0.4);
        }

        .btn-secondary {
            background: transparent;
            color: #4c51bf;
            border: 2px solid #4c51bf;
        }

        .btn-secondary:hover {
            background: rgba(76, 81, 191, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 81, 191, 0.2);
        }

        .btn-secondary.btn-warning {
            background: transparent;
            color: #ed8936;
            border: 2px solid #ed8936;
        }

        .btn-secondary.btn-warning:hover {
            background: rgba(237, 137, 54, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(237, 137, 54, 0.2);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
            border: 2px solid transparent;
        }

        .btn-danger:hover {
            box-shadow: 0 5px 15px rgba(229, 62, 62, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            border: 2px solid transparent;
        }

        .btn-warning:hover {
            box-shadow: 0 5px 15px rgba(237, 137, 54, 0.4);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3748;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #4c51bf;
        }

        select.form-control {
            cursor: pointer;
            background-color: white;
        }

        textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }

        .card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 15px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .card-header div {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
        }

        .item-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }

        .item-info {
            flex: 1;
        }

        .item-key {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .item-details {
            font-size: 12px;
            color: #718096;
        }

        .actions {
            display: flex;
            gap: 10px;
        }

        .copy-btn {
            background: none;
            border: none;
            color: #4c51bf;
            cursor: pointer;
            font-size: 12px;
            margin-left: 5px;
            padding: 2px 4px;
            border-radius: 3px;
            transition: background-color 0.2s;
        }

        .copy-btn:hover {
            background-color: rgba(76, 81, 191, 0.1);
        }

        #tokenKeyActions {
            display: flex;
            gap: 10px;
        }

        #tokenKeyActions .btn {
            white-space: nowrap;
            min-width: 80px;
            flex-shrink: 0;
        }

        .alert {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .alert.alert-top {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 9999;
            max-width: 600px;
            min-width: 300px;
            margin-bottom: 0;
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            animation: slideInDown 0.3s ease-out;
            text-align: center;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(-20px);
            }

            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }

        .alert-success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .alert-error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #718096;
        }

        /* mainContent loading 覆盖层样式 */
        #loadingOverlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            z-index: 10002;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
            pointer-events: auto;
            cursor: wait;
        }

        /* 当 loading 显示时，禁用 mainContent 交互 */
        #mainContent.loading-active {
            position: relative;
            pointer-events: none;
        }

        #mainContent.loading-active #loadingOverlay {
            pointer-events: auto;
        }

        #loadingOverlay .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #ffffff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        #loadingOverlay p {
            margin-top: 20px;
            font-size: 16px;
            font-weight: 500;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.7;
            }
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4c51bf;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .pricing-table {
            overflow-x: auto;
        }

        .pricing-table table {
            min-width: 600px;
        }

        .pricing-table input[type="text"],
        .pricing-table input[type="number"] {
            width: 100%;
            border: none;
            background: transparent;
            padding: 8px;
            font-size: 14px;
        }

        .pricing-table input[type="text"]:focus,
        .pricing-table input[type="number"]:focus {
            outline: 2px solid #4c51bf;
            outline-offset: -2px;
            border-radius: 4px;
            background: white;
        }

        .pricing-table tr:hover {
            background-color: #f8f9fa;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
            color: white;
            padding: 20px;
            border-radius: 12px 12px 0 0;
            position: relative;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .modal-close {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.3s;
        }

        .modal-close:hover {
            opacity: 1;
        }

        .modal-body {
            padding: 30px;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }

            .tabs {
                flex-direction: column;
            }

            .item {
                flex-direction: column;
                align-items: flex-start;
            }

            .actions {
                margin-top: 10px;
                width: 100%;
            }

            .auth-container {
                margin: 0 20px;
                padding: 30px 20px;
            }

            .header-info {
                position: static;
                text-align: center;
                margin-bottom: 10px;
            }

            .header h1 {
                font-size: 1.6rem;
            }

            .header.compact h1 {
                font-size: 1.2rem;
            }

            .auth-section {
                padding: 40px 20px;
            }

            /* 登录提示在移动端的样式优化 */
            #loginPrompt>div {
                flex-direction: column !important;
                gap: 10px !important;
            }

            #loginPrompt>div>div:last-child {
                margin-top: 0 !important;
                width: 100%;
                text-align: center;
                flex-shrink: 1 !important;
            }

            #loginPrompt>div>div:first-child {
                min-width: auto !important;
                text-align: center;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header" id="mainHeader">
            <div class="header-info">
                <a href="https://github.com/dreamhunter2333/awsl-one-api" target="_blank">🔗 GitHub</a>
                <span>v1.0.0</span>
            </div>
            <h1>🚀 Awsl One API</h1>
            <p>管理面板 - 频道、令牌、定价管理</p>
            <div id="logoutSection" style="display: none; position: absolute; top: 20px; right: 20px;">
                <button class="btn btn-secondary btn-warning" onclick="confirmLogout()"
                    style="font-size: 12px; padding: 8px 16px;">
                    🚪 退出登录
                </button>
            </div>
        </div>

        <div id="authSection" class="auth-section" style="display: none;">
            <div class="auth-container">
                <div class="auth-title">管理员登录</div>
                <form onsubmit="handleLogin(event)">
                    <input type="password" id="adminToken" class="auth-input" placeholder="请输入管理员令牌" />
                    <button type="submit" class="auth-btn">登录</button>
                </form>
            </div>
        </div>

        <!-- 登录模态框 -->
        <div id="loginModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">🔐 管理员登录</h3>
                    <button class="modal-close" onclick="closeLoginModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <form onsubmit="handleModalLogin(event)">
                        <div class="form-group">
                            <label for="modalAdminToken">管理员令牌</label>
                            <input type="password" id="modalAdminToken" class="form-control" placeholder="请输入管理员令牌"
                                autocomplete="off" />
                        </div>
                        <button type="submit" class="btn" style="width: 100%; margin: 0;">
                            🔑 登录
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div id="mainContent">
            <div class="tabs">
                <button class="tab" onclick="switchTab('home')">🏠 首页</button>
                <button class="tab admin-only" onclick="switchTab('system')" style="display: none;">⚙️ 系统管理</button>
                <button class="tab admin-only" onclick="switchTab('channels')" style="display: none;">🔗 频道管理</button>
                <button class="tab admin-only" onclick="switchTab('tokens')" style="display: none;">🔑 令牌管理</button>
                <button class="tab admin-only" onclick="switchTab('pricing')" style="display: none;">💰 定价管理</button>
                <button class="tab admin-only" onclick="switchTab('api-test')" style="display: none;">🧪 API 测试</button>
            </div>

            <!-- Home Tab -->
            <div id="home" class="tab-content">
                <!-- 未登录时的登录提示 -->
                <div id="loginPrompt" class="card"
                    style="background: #fff3cd; border-left: 4px solid #ffc107; margin-bottom: 20px; padding: 15px;">
                    <div
                        style="display: flex; align-items: center; justify-content: space-between; gap: 15px; flex-wrap: wrap;">
                        <div style="flex: 1; min-width: 200px;">
                            <p style="margin: 0; color: #856404; font-size: 14px;">
                                💡 要使用管理功能，请先进行身份验证
                            </p>
                        </div>
                        <div style="flex-shrink: 0;">
                            <button class="btn" onclick="showLoginModal()"
                                style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: #212529; margin: 0; font-size: 13px; padding: 6px 12px;">
                                🔑 立即登录
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 主要内容区域 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🚀 欢迎使用 AWSL One API</h3>
                    </div>
                    <div style="margin-bottom: 30px;">
                        <p style="font-size: 16px; color: #4a5568; line-height: 1.6; margin-bottom: 20px;">
                            AWSL One API 是一个强大的 API 代理和管理平台，为您提供统一的 AI 模型接口管理服务。
                        </p>

                        <div class="grid" style="margin-bottom: 30px;">
                            <div class="card"
                                style="margin-bottom: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                <h4 style="color: white; margin-bottom: 15px;">🔗 频道管理</h4>
                                <p style="margin: 0; opacity: 0.9; font-size: 14px;">配置和管理 AI 服务提供商的 API 端点，支持 Azure
                                    OpenAI 等多种平台。</p>
                            </div>
                            <div class="card"
                                style="margin-bottom: 0; background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%); color: white;">
                                <h4 style="color: white; margin-bottom: 15px;">🔑 令牌管理</h4>
                                <p style="margin: 0; opacity: 0.9; font-size: 14px;">生成和管理 API 访问令牌，控制用户访问权限和使用配额。</p>
                            </div>
                            <div class="card"
                                style="margin-bottom: 0; background: linear-gradient(135deg, #38b2ac 0%, #319795 100%); color: white;">
                                <h4 style="color: white; margin-bottom: 15px;">💰 定价管理</h4>
                                <p style="margin: 0; opacity: 0.9; font-size: 14px;">灵活配置不同 AI 模型的计费策略，精确控制使用成本。</p>
                            </div>
                            <div class="card"
                                style="margin-bottom: 0; background: linear-gradient(135deg, #ed64a6 0%, #d53f8c 100%); color: white;">
                                <h4 style="color: white; margin-bottom: 15px;">🧪 API 测试</h4>
                                <p style="margin: 0; opacity: 0.9; font-size: 14px;">内置 API 测试工具，快速验证配置和调试接口问题。</p>
                            </div>
                        </div>

                        <div
                            style="background: #f7fafc; padding: 20px; border-radius: 8px; border-left: 4px solid #4c51bf;">
                            <h4 style="margin: 0 0 15px 0; color: #2d3748; font-size: 16px;">⚡ 快速开始</h4>
                            <ol
                                style="margin: 0; padding-left: 20px; color: #4a5568; font-size: 14px; line-height: 1.6;">
                                <li style="margin-bottom: 8px;">首先初始化数据库以设置必要的数据表结构</li>
                                <li style="margin-bottom: 8px;">在频道管理中添加您的 AI 服务提供商配置</li>
                                <li style="margin-bottom: 8px;">创建访问令牌并设置适当的权限和配额</li>
                                <li style="margin-bottom: 8px;">配置模型定价策略以控制使用成本</li>
                                <li>使用 API 测试工具验证配置是否正常工作</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Management Tab -->
            <div id="system" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">⚙️ 系统管理</h3>
                    </div>
                    <p style="color: #4a5568; margin-bottom: 20px;">管理系统数据库，包括初始化表结构等操作。如果这是首次使用，请先进行数据库初始化。</p>
                    <button class="btn" onclick="initializeDatabase()">🔄 初始化数据库</button>
                </div>
            </div>

            <!-- Channels Tab -->
            <div id="channels" class="tab-content">
                <!-- Channel List View -->
                <div id="channelListView">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">频道列表</h3>
                            <div>
                                <button class="btn" onclick="showChannelForm()">➕ 添加频道</button>
                                <button class="btn btn-secondary" onclick="loadChannels()">🔄 刷新</button>
                            </div>
                        </div>
                        <div id="channelsList" class="item-list"></div>
                    </div>
                </div>

                <!-- Channel Form View -->
                <div id="channelFormView" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title" id="channelFormTitle">添加频道</h3>
                            <div>
                                <button class="btn btn-secondary" onclick="toggleChannelEditMode()"
                                    id="channelModeToggle">📝
                                    切换到JSON模式</button>
                                <button class="btn" onclick="saveChannel()">💾 保存频道</button>
                                <button class="btn btn-secondary" onclick="showChannelList()">← 返回列表</button>
                            </div>
                        </div>

                        <!-- 共用标识字段 -->
                        <div class="form-group">
                            <label for="channelKey">频道标识</label>
                            <input type="text" id="channelKey" class="form-control" placeholder="例如: azure-openai-1">
                        </div>

                        <!-- 表单模式 -->
                        <div id="channelFormMode">
                            <div class="form-group">
                                <label for="channelName">频道名称</label>
                                <input type="text" id="channelName" class="form-control"
                                    placeholder="例如: My OpenAI Channel">
                            </div>
                            <div class="form-group">
                                <label for="channelType">频道类型</label>
                                <select id="channelType" class="form-control" onchange="toggleApiVersionField()">
                                    <option value="azure-openai">Azure OpenAI</option>
                                    <option value="openai">OpenAI</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="channelEndpoint">API 端点</label>
                                <input type="text" id="channelEndpoint" class="form-control"
                                    placeholder="例如: https://xxx.openai.azure.com/">
                            </div>
                            <div class="form-group">
                                <label for="channelApiKey">API 密钥</label>
                                <input type="password" id="channelApiKey" class="form-control"
                                    placeholder="your-api-key">
                            </div>
                            <div class="form-group" id="apiVersionGroup">
                                <label for="channelApiVersion">API 版本</label>
                                <input type="text" id="channelApiVersion" class="form-control"
                                    placeholder="例如: 2024-02-01">
                            </div>
                            <div class="form-group">
                                <label for="deploymentMapper">模型部署映射 (JSON)</label>
                                <textarea id="deploymentMapper" class="form-control" rows="4"
                                    placeholder='{"gpt-35-turbo": "gpt-35-turbo", "gpt-4": "gpt-4"}'></textarea>
                                <small style="color: #666; font-size: 12px;">配置模型名称映射关系（主要用于Azure OpenAI）</small>
                            </div>
                            <div class="form-group">
                                <label for="additionalConfig">其他配置 (JSON，可选)</label>
                                <textarea id="additionalConfig" class="form-control" rows="3"
                                    placeholder='{"timeout": 30, "max_retries": 3}'></textarea>
                                <small style="color: #666; font-size: 12px;">其他自定义配置参数</small>
                            </div>
                        </div>

                        <!-- JSON模式 -->
                        <div id="channelJsonMode" style="display: none;">
                            <div class="form-group">
                                <label for="channelValueJson">频道配置 (JSON)</label>
                                <textarea id="channelValueJson" class="form-control" rows="15"
                                    placeholder='{"name": "Azure OpenAI", "type": "azure-openai", "endpoint": "https://xxx.openai.azure.com/", "api_key": "your-api-key", "api_version": "2024-02-01", "deployment_mapper": {"gpt-35-turbo": "gpt-35-turbo", "gpt-4": "gpt-4"}}'></textarea>
                                <small style="color: #666; font-size: 12px;">直接编辑完整的频道配置JSON</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tokens Tab -->
            <div id="tokens" class="tab-content">
                <!-- Token List View -->
                <div id="tokenListView">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">令牌列表</h3>
                            <div>
                                <button class="btn" onclick="showTokenForm()">➕ 添加令牌</button>
                                <button class="btn btn-secondary" onclick="loadTokens()">🔄 刷新</button>
                            </div>
                        </div>
                        <div id="tokensList" class="item-list"></div>
                    </div>
                </div>

                <!-- Token Form View -->
                <div id="tokenFormView" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title" id="tokenFormTitle">添加令牌</h3>
                            <div>
                                <button class="btn btn-secondary" onclick="toggleTokenEditMode()"
                                    id="tokenModeToggle">📝
                                    切换到JSON模式</button>
                                <button class="btn" onclick="saveToken()">💾 保存令牌</button>
                                <button class="btn btn-secondary" onclick="showTokenList()">← 返回列表</button>
                            </div>
                        </div>

                        <!-- 共用标识字段 -->
                        <div class="form-group">
                            <label for="tokenKey">令牌标识</label>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <input type="text" id="tokenKey" class="form-control" placeholder="输入令牌标识或点击生成">
                                <div id="tokenKeyActions">
                                    <button type="button" id="generateTokenBtn" class="btn btn-secondary"
                                        onclick="generateTokenKey()">🎲 生成</button>
                                    <button type="button" id="copyTokenBtn" class="btn btn-secondary"
                                        onclick="copyTokenKey()" style="display: none;">📋 复制</button>
                                </div>
                            </div>
                        </div>

                        <!-- 表单模式 -->
                        <div id="tokenFormMode">
                            <div class="form-group">
                                <label for="tokenName">令牌名称</label>
                                <input type="text" id="tokenName" class="form-control" placeholder="例如: 用户令牌1">
                            </div>
                            <div class="form-group">
                                <label for="channelKeysInput">允许访问的频道</label>
                                <input type="text" id="channelKeysInput" class="form-control"
                                    placeholder="输入频道标识，多个用逗号分隔，留空表示允许所有频道">
                                <small style="color: #666; font-size: 12px;">例如: azure-openai-1, azure-openai-2
                                    (留空表示允许访问所有频道)</small>
                            </div>
                            <div class="form-group">
                                <label for="totalQuotaInput">总配额</label>
                                <input type="number" id="totalQuotaInput" class="form-control" placeholder="1000000"
                                    min="0" step="1">
                                <div style="margin-top: 10px;">
                                    <small
                                        style="color: #666; font-size: 12px; display: block; margin-bottom: 8px;">快速选择:</small>
                                    <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                                        <button type="button" class="btn btn-secondary"
                                            style="padding: 6px 12px; font-size: 12px; margin: 0;"
                                            onclick="setQuotaValue(1000000)">$1</button>
                                        <button type="button" class="btn btn-secondary"
                                            style="padding: 6px 12px; font-size: 12px; margin: 0;"
                                            onclick="setQuotaValue(5000000)">$5</button>
                                        <button type="button" class="btn btn-secondary"
                                            style="padding: 6px 12px; font-size: 12px; margin: 0;"
                                            onclick="setQuotaValue(10000000)">$10</button>
                                        <button type="button" class="btn btn-secondary"
                                            style="padding: 6px 12px; font-size: 12px; margin: 0;"
                                            onclick="setQuotaValue(20000000)">$20</button>
                                        <button type="button" class="btn btn-secondary"
                                            style="padding: 6px 12px; font-size: 12px; margin: 0;"
                                            onclick="setQuotaValue(50000000)">$50</button>
                                    </div>
                                </div>
                                <small style="color: #666; font-size: 12px; margin-top: 8px; display: block;">设置令牌的总使用配额
                                    (基础单位：1百万token =
                                    $1.00，例如：输入1000000表示1美元的配额)</small>
                            </div>
                        </div>

                        <!-- JSON模式 -->
                        <div id="tokenJsonMode" style="display: none;">
                            <div class="form-group">
                                <label for="tokenValueJson">令牌配置 (JSON)</label>
                                <textarea id="tokenValueJson" class="form-control" rows="12"
                                    placeholder='{"name": "用户令牌1", "channel_keys": ["azure-openai-1", "azure-openai-2"], "total_quota": 1000000}'></textarea>
                                <small style="color: #666; font-size: 12px;">直接编辑完整的令牌配置JSON</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pricing Tab -->
            <div id="pricing" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">定价配置</h3>
                        <div>
                            <button class="btn btn-secondary" onclick="togglePricingEditMode()"
                                id="pricingModeToggle">📝
                                切换到JSON模式</button>
                            <button class="btn btn-secondary" onclick="addPricingModel()" id="addPricingModelBtn">➕
                                添加模型</button>
                            <button class="btn" onclick="updatePricing()">💾 更新定价</button>
                            <button class="btn btn-secondary" onclick="loadPricing()">🔄 刷新</button>
                        </div>
                    </div>
                    <div
                        style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 20px; border-left: 4px solid #4c51bf;">
                        <h4 style="margin: 0 0 10px 0; color: #2d3748; font-size: 14px;">💡 定价说明</h4>
                        <p style="margin: 0; font-size: 13px; color: #4a5568; line-height: 1.5;">
                            <strong>基础配额单位：1百万token = $1.00</strong> | 定价配置为不同模型相对于基础单位的倍率 |
                            例如：gpt-4的input倍率为30，表示1百万token消耗30美元配额
                        </p>
                    </div>
                    <div id="pricingContent">
                        <!-- 表格模式 -->
                        <div id="pricingTableMode" class="pricing-table">
                            <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                                <thead>
                                    <tr style="background-color: #f8f9fa; border-bottom: 2px solid #e9ecef;">
                                        <th
                                            style="padding: 12px; text-align: left; font-weight: 600; color: #2d3748; border: 1px solid #e9ecef;">
                                            模型名称</th>
                                        <th
                                            style="padding: 12px; text-align: left; font-weight: 600; color: #2d3748; border: 1px solid #e9ecef;">
                                            输入倍率</th>
                                        <th
                                            style="padding: 12px; text-align: left; font-weight: 600; color: #2d3748; border: 1px solid #e9ecef;">
                                            输出倍率</th>
                                        <th
                                            style="padding: 12px; text-align: center; font-weight: 600; color: #2d3748; border: 1px solid #e9ecef; width: 100px;">
                                            操作</th>
                                    </tr>
                                </thead>
                                <tbody id="pricingTableBody">
                                    <!-- 动态生成的定价行 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- JSON模式 -->
                        <div id="pricingJsonMode" style="display: none;">
                            <div class="form-group">
                                <label for="pricingConfigJson">定价配置 (JSON)</label>
                                <textarea id="pricingConfigJson" class="form-control" rows="15"
                                    placeholder='{"gpt-3.5-turbo": {"input": 0.001, "output": 0.002}, "gpt-4": {"input": 0.03, "output": 0.06}}'></textarea>
                                <small style="color: #666; font-size: 12px; margin-top: 8px; display: block;">
                                    配置格式：模型名称 -> {input: 输入倍率, output: 输出倍率}。倍率基于1百万token=$1.00计算
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API Test Tab -->
            <div id="api-test" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">API 测试</h3>
                        <div>
                            <button class="btn" onclick="sendApiTest()">🚀 发送请求</button>
                            <button class="btn btn-secondary" onclick="clearApiTest()">🧹 清空</button>
                        </div>
                    </div>
                    <div class="grid">
                        <div>
                            <div class="form-group">
                                <label for="apiTestToken">Bearer Token</label>
                                <input type="text" id="apiTestToken" class="form-control"
                                    placeholder="输入你的 API Token (sk-...)">
                                <small style="color: #666; font-size: 12px;">从令牌管理页面复制你的 API Token</small>
                            </div>
                            <div class="form-group">
                                <label for="apiTestRequest">请求 JSON</label>
                                <textarea id="apiTestRequest" class="form-control" rows="12" placeholder='输入请求 JSON...'>{
  "model": "gpt-4",
  "messages": [
    {
      "role": "user",
      "content": "Hello, world!"
    }
  ]
}</textarea>
                                <small style="color: #666; font-size: 12px;">请求将发送到 /v1/chat/completions 端点</small>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label for="apiTestResponse">响应 JSON</label>
                                <textarea id="apiTestResponse" class="form-control" rows="16"
                                    placeholder="API 响应将显示在这里..." readonly></textarea>
                                <div id="apiTestStatus"
                                    style="margin-top: 10px; padding: 10px; border-radius: 6px; display: none;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // === 全局变量 ===
        let adminToken = '';
        let currentEditingChannel = null;
        let currentEditingToken = null;

        // === 通用工具函数 ===

        // DOM元素操作工具类
        const DOMUtils = {
            // 获取元素并检查是否存在
            getElement(id) {
                const element = document.getElementById(id);
                if (!element) {
                    console.warn(`Element with id '${id}' not found`);
                }
                return element;
            },

            // 显示/隐藏元素
            show(elementOrId) {
                const element = typeof elementOrId === 'string' ? this.getElement(elementOrId) : elementOrId;
                if (element) element.style.display = 'block';
            },

            hide(elementOrId) {
                const element = typeof elementOrId === 'string' ? this.getElement(elementOrId) : elementOrId;
                if (element) element.style.display = 'none';
            },

            // 切换class
            toggleClass(elementOrId, className, force) {
                const element = typeof elementOrId === 'string' ? this.getElement(elementOrId) : elementOrId;
                if (element) {
                    if (force !== undefined) {
                        element.classList.toggle(className, force);
                    } else {
                        element.classList.toggle(className);
                    }
                }
            },

            // 清空输入框
            clearInput(id) {
                const element = this.getElement(id);
                if (element) element.value = '';
            },

            // 设置输入框值
            setValue(id, value) {
                const element = this.getElement(id);
                if (element) element.value = value || '';
            },

            // 获取输入框值
            getValue(id) {
                const element = this.getElement(id);
                return element ? element.value.trim() : '';
            },

            // 聚焦元素
            focus(id, delay = 0) {
                const element = this.getElement(id);
                if (element) {
                    if (delay > 0) {
                        setTimeout(() => element.focus(), delay);
                    } else {
                        element.focus();
                    }
                }
            }
        };

        // 表单状态管理工具类
        const FormUtils = {
            // 设置输入框为只读状态
            setReadonly(id, readonly = true) {
                const element = DOMUtils.getElement(id);
                if (element) {
                    element.readOnly = readonly;
                    element.disabled = readonly;
                    element.style.cursor = readonly ? 'not-allowed' : 'text';
                    element.style.backgroundColor = readonly ? '#f8f9fa' : '';
                    element.placeholder = readonly ? '编辑时不可修改' : element.getAttribute('data-original-placeholder') || '';
                }
            },

            // 保存原始placeholder
            saveOriginalPlaceholder(id) {
                const element = DOMUtils.getElement(id);
                if (element && !element.getAttribute('data-original-placeholder')) {
                    element.setAttribute('data-original-placeholder', element.placeholder);
                }
            },

            // 验证必填字段
            validateRequired(fields) {
                for (const field of fields) {
                    const value = DOMUtils.getValue(field.id);
                    if (!value) {
                        showAlert(field.message || `请填写${field.name}`, 'error');
                        return false;
                    }
                }
                return true;
            },

            // 验证JSON格式
            validateJSON(jsonString, errorMessage = 'JSON格式错误') {
                try {
                    return JSON.parse(jsonString);
                } catch (error) {
                    showAlert(errorMessage, 'error');
                    return null;
                }
            }
        };

        // Tab管理工具类
        const TabUtils = {
            // 激活tab
            activateTab(tabName) {
                // 隐藏所有tab内容
                document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
                document.querySelectorAll('.tab').forEach(btn => btn.classList.remove('active'));

                // 显示目标tab
                const targetTab = DOMUtils.getElement(tabName);
                if (targetTab) targetTab.classList.add('active');

                // 激活对应按钮
                const targetButton = document.querySelector(`.tab[onclick="switchTab('${tabName}')"]`);
                if (targetButton) targetButton.classList.add('active');
            },

            // 设置管理员tabs可见性
            setAdminTabsVisible(visible = true) {
                document.querySelectorAll('.admin-only').forEach(tab => {
                    tab.style.display = visible ? 'block' : 'none';
                });
            }
        };

        // 认证状态管理
        const AuthManager = {
            // 检查是否已登录
            isAuthenticated() {
                return !!adminToken;
            },

            // 设置token
            setToken(token) {
                adminToken = token;
                localStorage.setItem('adminToken', token);
            },

            // 清除token
            clearToken() {
                adminToken = '';
                localStorage.removeItem('adminToken');
            },

            // 从localStorage恢复token
            restoreToken() {
                const savedToken = localStorage.getItem('adminToken');
                if (savedToken) {
                    adminToken = savedToken;
                    return true;
                }
                return false;
            }
        };

        // === 认证相关函数 ===

        // 显示登录模态框
        function showLoginModal() {
            DOMUtils.show('loginModal');
            DOMUtils.clearInput('modalAdminToken');
            DOMUtils.focus('modalAdminToken', 100);
        }

        // 关闭登录模态框
        function closeLoginModal() {
            DOMUtils.hide('loginModal');
        }

        // 处理模态框登录表单提交
        function handleModalLogin(event) {
            event.preventDefault();
            authenticateFromModal();
        }

        // 模态框身份验证
        function authenticateFromModal() {
            const token = DOMUtils.getValue('modalAdminToken');
            if (!token) {
                showAlert('请输入管理员令牌', 'error');
                return;
            }

            AuthManager.setToken(token);
            closeLoginModal();
            showMainContent();
            showAlert('登录成功！', 'success');
        }

        // 显示登录页面 (保留兼容性)
        function showLoginPage() {
            showLoginModal();
        }

        // 显示主页面（未登录状态）
        function showHomePage() {
            DOMUtils.hide('authSection');
            DOMUtils.show('mainContent');
            DOMUtils.hide('logoutSection');
            DOMUtils.toggleClass('mainHeader', 'compact', true);

            // 显示登录提示
            DOMUtils.show('loginPrompt');

            // 隐藏管理员专用的tabs
            TabUtils.setAdminTabsVisible(false);

            // 激活首页tab
            TabUtils.activateTab('home');
        }

        // 处理登录表单提交（支持回车键）
        function handleLogin(event) {
            event.preventDefault();
            authenticate();
        }

        // Authentication
        function authenticate() {
            const token = DOMUtils.getValue('adminToken');
            if (!token) {
                showAlert('请输入管理员令牌', 'error');
                return;
            }

            AuthManager.setToken(token);
            showMainContent();
        }

        // 显示主内容区域（已登录状态）
        function showMainContent() {
            DOMUtils.hide('authSection');
            DOMUtils.show('mainContent');
            DOMUtils.show('logoutSection');
            DOMUtils.toggleClass('mainHeader', 'compact', true);

            // 隐藏登录提示
            DOMUtils.hide('loginPrompt');

            // 显示管理员专用的tabs
            TabUtils.setAdminTabsVisible(true);

            // 激活首页tab
            TabUtils.activateTab('home');
        }

        // 确认退出登录
        function confirmLogout() {
            if (confirm('确定要退出登录吗？\n\n退出后您需要重新输入管理员令牌才能使用管理功能。')) {
                logout();
            }
        }

        // 退出登录
        function logout() {
            AuthManager.clearToken();
            showHomePage();
            DOMUtils.clearInput('adminToken');
            showAlert('已退出登录', 'success');
        }

        // Tab switching
        function switchTab(tabName) {
            // 检查是否为管理员专用tab且用户未登录
            const adminOnlyTabs = ['system', 'channels', 'tokens', 'pricing', 'api-test'];
            if (adminOnlyTabs.includes(tabName) && !AuthManager.isAuthenticated()) {
                showAlert('请先登录后再访问管理功能', 'error');
                return;
            }

            // 隐藏所有表单视图，显示列表视图
            if (tabName === 'channels') {
                showChannelList();
            } else if (tabName === 'tokens') {
                showTokenList();
            }

            // 激活tab
            TabUtils.activateTab(tabName);

            // 切换tab时重新获取数据
            const tabDataLoaders = {
                'channels': loadChannels,
                'tokens': loadTokens,
                'pricing': loadPricing
            };

            const loader = tabDataLoaders[tabName];
            if (loader) {
                loader();
            }
        }

        // === API请求工具函数 ===

        // API request helper
        async function apiRequest(url, method = 'GET', body = null, showGlobalLoading = true, loadingMessage = '正在处理请求...') {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'x-admin-token': adminToken
                }
            };

            if (body) {
                options.body = JSON.stringify(body);
            }

            // 显示全局 loading
            if (showGlobalLoading) {
                showLoading(loadingMessage);
            }

            try {
                const response = await fetch(url, options);
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const result = await response.json();

                // 处理 CommonResponse 格式
                if (result.success === false) {
                    throw new Error(result.message || '请求失败');
                }

                // 返回实际数据，如果有 data 字段则返回 data，否则返回整个结果
                return result.data !== undefined ? result.data : result;
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            } finally {
                // 无论成功还是失败都隐藏 loading
                if (showGlobalLoading) {
                    hideLoading();
                }
            }
        }

        // Database functions
        async function initializeDatabase() {
            try {
                await apiRequest('/api/admin/db_initialize', 'POST', null, true, '正在初始化数据库...');
                showAlert('数据库初始化成功！', 'success');
            } catch (error) {
                showAlert(`数据库初始化失败: ${error.message}`, 'error');
            }
        }

        // === 表单编辑模式管理 ===

        // 编辑模式管理类
        class EditModeManager {
            constructor(formModeId, jsonModeId, toggleBtnId, formModeText, jsonModeText) {
                this.formModeElement = DOMUtils.getElement(formModeId);
                this.jsonModeElement = DOMUtils.getElement(jsonModeId);
                this.toggleBtn = DOMUtils.getElement(toggleBtnId);
                this.formModeText = formModeText;
                this.jsonModeText = jsonModeText;
                this.currentMode = 'form';
            }

            // 切换编辑模式
            toggle(formToJsonConverter, jsonToFormConverter) {
                if (this.currentMode === 'form') {
                    this._switchToJsonMode(formToJsonConverter);
                } else {
                    this._switchToFormMode(jsonToFormConverter);
                }
            }

            _switchToJsonMode(converter) {
                if (converter) {
                    const data = converter();
                    if (data && this.jsonModeElement) {
                        const textarea = this.jsonModeElement.querySelector('textarea');
                        if (textarea) {
                            textarea.value = JSON.stringify(data, null, 2);
                        }
                    }
                }

                DOMUtils.hide(this.formModeElement);
                DOMUtils.show(this.jsonModeElement);
                if (this.toggleBtn) this.toggleBtn.textContent = this.formModeText;
                this.currentMode = 'json';
            }

            _switchToFormMode(converter) {
                if (converter) {
                    try {
                        const textarea = this.jsonModeElement?.querySelector('textarea');
                        const jsonValue = textarea?.value;
                        if (jsonValue) {
                            const parsed = JSON.parse(jsonValue);
                            converter(parsed);
                        }
                    } catch (error) {
                        console.warn('JSON格式错误，无法转换到表单模式:', error);
                    }
                }

                DOMUtils.show(this.formModeElement);
                DOMUtils.hide(this.jsonModeElement);
                if (this.toggleBtn) this.toggleBtn.textContent = this.jsonModeText;
                this.currentMode = 'form';
            }

            // 重置到表单模式
            resetToFormMode() {
                this.currentMode = 'form';
                DOMUtils.show(this.formModeElement);
                DOMUtils.hide(this.jsonModeElement);
                if (this.toggleBtn) this.toggleBtn.textContent = this.jsonModeText;
            }

            // 获取当前模式
            getCurrentMode() {
                return this.currentMode;
            }
        }

        // === CHANNEL FUNCTIONS ===

        // 全局变量跟踪编辑模式
        let channelEditMode = 'form';
        let tokenEditMode = 'form';
        let pricingEditMode = 'table';

        // 创建频道编辑模式管理器
        const channelModeManager = new EditModeManager(
            'channelFormMode',
            'channelJsonMode',
            'channelModeToggle',
            '📋 切换到表单模式',
            '📝 切换到JSON模式'
        );

        // 切换频道编辑模式
        function toggleChannelEditMode() {
            channelModeManager.toggle(
                collectChannelFormData,
                (parsed) => {
                    const key = DOMUtils.getValue('channelKey');
                    fillChannelForm(key, parsed);
                }
            );
            channelEditMode = channelModeManager.getCurrentMode();
        }

        // 收集频道表单数据
        function collectChannelFormData() {
            const requiredFields = [
                { id: 'channelName', name: '频道名称' },
                { id: 'channelEndpoint', name: 'API端点' },
                { id: 'channelApiKey', name: 'API密钥' }
            ];

            // 检查必填字段
            for (const field of requiredFields) {
                if (!DOMUtils.getValue(field.id)) {
                    return null;
                }
            }

            const config = {
                name: DOMUtils.getValue('channelName'),
                type: DOMUtils.getValue('channelType'),
                endpoint: DOMUtils.getValue('channelEndpoint'),
                api_key: DOMUtils.getValue('channelApiKey')
            };

            // 可选字段
            const apiVersion = DOMUtils.getValue('channelApiVersion');
            if (apiVersion) {
                config.api_version = apiVersion;
            }

            // 处理JSON字段
            const deploymentMapperText = DOMUtils.getValue('deploymentMapper');
            if (deploymentMapperText) {
                const parsed = FormUtils.validateJSON(deploymentMapperText, '部署映射JSON格式错误');
                if (parsed) config.deployment_mapper = parsed;
            }

            const additionalConfigText = DOMUtils.getValue('additionalConfig');
            if (additionalConfigText) {
                const parsed = FormUtils.validateJSON(additionalConfigText, '其他配置JSON格式错误');
                if (parsed) Object.assign(config, parsed);
            }

            return config;
        }

        // 填充频道表单
        function fillChannelForm(key, config) {
            const fieldMappings = [
                { id: 'channelKey', value: key },
                { id: 'channelName', value: config.name },
                { id: 'channelType', value: config.type },
                { id: 'channelEndpoint', value: config.endpoint },
                { id: 'channelApiKey', value: config.api_key },
                { id: 'channelApiVersion', value: config.api_version }
            ];

            fieldMappings.forEach(field => DOMUtils.setValue(field.id, field.value));

            // 处理特殊字段
            if (config.deployment_mapper) {
                DOMUtils.setValue('deploymentMapper', JSON.stringify(config.deployment_mapper, null, 2));
            } else {
                DOMUtils.clearInput('deploymentMapper');
            }

            // 处理其他配置
            const additionalConfig = { ...config };
            ['name', 'type', 'endpoint', 'api_key', 'api_version', 'deployment_mapper'].forEach(key => {
                delete additionalConfig[key];
            });

            if (Object.keys(additionalConfig).length > 0) {
                DOMUtils.setValue('additionalConfig', JSON.stringify(additionalConfig, null, 2));
            } else {
                DOMUtils.clearInput('additionalConfig');
            }
        }

        // 切换API版本字段的显示状态
        function toggleApiVersionField() {
            const channelType = DOMUtils.getValue('channelType');
            const apiVersionGroup = DOMUtils.getElement('apiVersionGroup');

            if (channelType === 'azure-openai') {
                DOMUtils.show(apiVersionGroup);
            } else {
                DOMUtils.hide(apiVersionGroup);
                DOMUtils.clearInput('channelApiVersion');
            }
        }

        // 显示频道列表
        function showChannelList() {
            DOMUtils.show('channelListView');
            DOMUtils.hide('channelFormView');
            currentEditingChannel = null;
        }

        // 显示频道表单
        function showChannelForm(isEdit = false) {
            DOMUtils.hide('channelListView');
            DOMUtils.show('channelFormView');
            DOMUtils.setValue('channelFormTitle', isEdit ? '编辑频道' : '添加频道');

            if (!isEdit) {
                clearChannelForm();
            }
        }

        // 清空频道表单
        function clearChannelForm() {
            // 清空所有输入字段
            const fieldIds = [
                'channelKey', 'channelName', 'channelEndpoint',
                'channelApiKey', 'channelApiVersion', 'deploymentMapper',
                'additionalConfig', 'channelValueJson'
            ];

            fieldIds.forEach(id => DOMUtils.clearInput(id));

            // 重置表单状态
            FormUtils.setReadonly('channelKey', false);
            DOMUtils.setValue('channelType', 'openai');
            DOMUtils.getElement('channelKey').placeholder = '例如: openai-1';

            // 重置编辑模式
            channelModeManager.resetToFormMode();
            channelEditMode = 'form';
            currentEditingChannel = null;

            // 确保API版本字段根据类型正确显示
            toggleApiVersionField();
        }

        // 编辑频道
        function editChannelFromList(key, originalValue) {
            currentEditingChannel = key;

            const parsed = FormUtils.validateJSON(originalValue, '频道配置格式错误');
            if (!parsed) return;

            // 设置key字段为只读
            FormUtils.saveOriginalPlaceholder('channelKey');
            FormUtils.setReadonly('channelKey', true);
            DOMUtils.setValue('channelKey', key);

            if (channelEditMode === 'form') {
                fillChannelForm(key, parsed);
            } else {
                DOMUtils.setValue('channelValueJson', JSON.stringify(parsed, null, 2));
            }

            toggleApiVersionField();
            showChannelForm(true);
        }

        // 保存频道
        async function saveChannel() {
            const key = DOMUtils.getValue('channelKey');
            if (!key) {
                showAlert('请填写频道标识', 'error');
                return;
            }

            let config;
            if (channelEditMode === 'form') {
                config = collectChannelFormData();
                if (!config) {
                    showAlert('请填写所有必填字段（频道名称、端点、API密钥）', 'error');
                    return;
                }
            } else {
                const jsonValue = DOMUtils.getValue('channelValueJson');
                if (!jsonValue) {
                    showAlert('请填写配置JSON', 'error');
                    return;
                }
                config = FormUtils.validateJSON(jsonValue, 'JSON格式错误，请检查配置格式');
                if (!config) return;
            }

            try {
                await apiRequest(`/api/admin/channel/${encodeURIComponent(key)}`, 'POST', config, true, '正在保存频道...');
                showAlert('频道保存成功！', 'success');
                clearChannelForm();
                showChannelList();
                loadChannels();
            } catch (error) {
                showAlert(`保存频道失败: ${error.message}`, 'error');
            }
        }

        // Channel functions
        async function loadChannels() {
            try {
                const channels = await apiRequest('/api/admin/channel', 'GET', null, true);
                displayChannels(channels);
            } catch (error) {
                DOMUtils.getElement('channelsList').innerHTML = `<div class="alert alert-error">加载频道失败: ${error.message}</div>`;
            }
        }

        async function deleteChannel(key) {
            if (!confirm('确定要删除这个频道吗？')) return;

            try {
                await apiRequest(`/api/admin/channel/${encodeURIComponent(key)}`, 'DELETE', null, true, '正在删除频道...');
                showAlert('频道删除成功！', 'success');
                loadChannels();
            } catch (error) {
                showAlert(`删除频道失败: ${error.message}`, 'error');
            }
        }

        // 显示频道列表的通用函数
        function displayChannels(channels) {
            const container = DOMUtils.getElement('channelsList');
            if (!container) return;

            // 处理空数据的情况
            if (!channels || !Array.isArray(channels) || channels.length === 0) {
                container.innerHTML = '<div class="alert alert-error">暂无频道数据</div>';
                return;
            }

            container.innerHTML = channels.map(item => {
                let parsedValue = {};
                try {
                    parsedValue = JSON.parse(item.value);
                } catch {
                    parsedValue = { name: 'Invalid JSON', type: 'azure-openai', endpoint: '' };
                }

                const name = parsedValue.name || 'No name';
                const type = parsedValue.type || 'Unknown';
                const endpoint = (parsedValue.endpoint || '');
                const endpointDisplay = endpoint.length > 50 ? endpoint.substring(0, 50) + '...' : endpoint;

                return `
                    <div class="item">
                        <div class="item-info">
                            <div class="item-key">${name}</div>
                            <div class="item-details">
                                ${item.key || 'Unknown'} | 类型: ${type} | 端点: ${endpointDisplay}
                            </div>
                        </div>
                        <div class="actions">
                            <button class="btn btn-secondary" onclick="editChannelFromList('${item.key}', '${item.value.replace(/'/g, '\\\'').replace(/"/g, '&quot;')}')">✏️ 编辑</button>
                            <button class="btn btn-danger" onclick="deleteChannel('${item.key}')">🗑️ 删除</button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // === TOKEN FUNCTIONS ===

        // 创建令牌编辑模式管理器
        const tokenModeManager = new EditModeManager(
            'tokenFormMode',
            'tokenJsonMode',
            'tokenModeToggle',
            '📋 切换到表单模式',
            '📝 切换到JSON模式'
        );

        // 切换令牌编辑模式
        function toggleTokenEditMode() {
            tokenModeManager.toggle(
                () => {
                    const formData = collectTokenFormData();
                    delete formData.key; // 从配置中移除key，因为key现在是共用的
                    return formData;
                },
                (parsed) => {
                    const key = DOMUtils.getValue('tokenKey');
                    fillTokenForm(key, parsed);
                }
            );
            tokenEditMode = tokenModeManager.getCurrentMode();
        }

        // 收集令牌表单数据
        function collectTokenFormData() {
            const channelKeysInput = DOMUtils.getValue('channelKeysInput');
            const channelKeys = channelKeysInput
                ? channelKeysInput.split(',').map(key => key.trim()).filter(key => key.length > 0)
                : [];

            return {
                key: DOMUtils.getValue('tokenKey'),
                name: DOMUtils.getValue('tokenName'),
                channel_keys: channelKeys,
                total_quota: parseFloat(DOMUtils.getValue('totalQuotaInput')) || 0
            };
        }

        // 填充令牌表单
        function fillTokenForm(key, config) {
            DOMUtils.setValue('tokenName', config.name);
            DOMUtils.setValue('tokenKey', key);

            const channelKeysStr = config.channel_keys && Array.isArray(config.channel_keys)
                ? config.channel_keys.join(', ')
                : '';
            DOMUtils.setValue('channelKeysInput', channelKeysStr);
            DOMUtils.setValue('totalQuotaInput', config.total_quota);
        }

        // Token按钮状态管理类
        class TokenButtonManager {
            static setEditMode(isEdit) {
                const tokenKeyInput = DOMUtils.getElement('tokenKey');
                const generateBtn = DOMUtils.getElement('generateTokenBtn');
                const copyBtn = DOMUtils.getElement('copyTokenBtn');

                if (isEdit) {
                    // 编辑模式：只显示复制按钮，token key 不可编辑
                    FormUtils.setReadonly('tokenKey', true);
                    DOMUtils.hide(generateBtn);
                    DOMUtils.show(copyBtn);
                } else {
                    // 创建模式：显示生成按钮，token key 可编辑
                    FormUtils.setReadonly('tokenKey', false);
                    DOMUtils.show(generateBtn);
                    DOMUtils.hide(copyBtn);
                    if (tokenKeyInput) {
                        tokenKeyInput.placeholder = '输入令牌标识或点击生成';
                    }
                }
            }
        }

        // 显示令牌列表
        function showTokenList() {
            DOMUtils.show('tokenListView');
            DOMUtils.hide('tokenFormView');
            currentEditingToken = null;
        }

        // 显示令牌表单
        function showTokenForm(isEdit = false) {
            DOMUtils.hide('tokenListView');
            DOMUtils.show('tokenFormView');

            const titleElement = DOMUtils.getElement('tokenFormTitle');
            if (titleElement) titleElement.textContent = isEdit ? '编辑令牌' : '添加令牌';

            TokenButtonManager.setEditMode(isEdit);

            if (!isEdit) {
                clearTokenForm();
                generateTokenKey(); // 自动生成token标识
            }
        }

        // 清空令牌表单
        function clearTokenForm() {
            // 清空所有字段
            const fieldIds = ['tokenName', 'channelKeysInput', 'totalQuotaInput', 'tokenValueJson'];
            fieldIds.forEach(id => DOMUtils.clearInput(id));

            // 重置token key输入框状态
            FormUtils.saveOriginalPlaceholder('tokenKey');
            FormUtils.setReadonly('tokenKey', false);
            DOMUtils.clearInput('tokenKey');

            const tokenKeyInput = DOMUtils.getElement('tokenKey');
            if (tokenKeyInput) {
                tokenKeyInput.placeholder = '输入令牌标识或点击生成';
            }

            // 重置编辑模式
            tokenModeManager.resetToFormMode();
            tokenEditMode = 'form';
            currentEditingToken = null;
        }

        // 生成token key
        function generateTokenKey() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let result = 'sk-';
            for (let i = 0; i < 48; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            DOMUtils.setValue('tokenKey', result);
        }

        // 复制token key
        function copyTokenKey() {
            const tokenKey = DOMUtils.getValue('tokenKey');
            if (!tokenKey) {
                showAlert('请先生成令牌标识', 'error');
                return;
            }
            copyToClipboard(tokenKey);
        }

        // 设置配额值
        function setQuotaValue(value) {
            DOMUtils.setValue('totalQuotaInput', value);
        }

        // 从列表编辑令牌
        function editTokenFromList(key, originalValue) {
            currentEditingToken = key;

            const parsed = FormUtils.validateJSON(originalValue, '令牌配置格式错误');
            if (!parsed) return;

            // 设置key字段状态
            FormUtils.saveOriginalPlaceholder('tokenKey');
            FormUtils.setReadonly('tokenKey', true);
            DOMUtils.setValue('tokenKey', key);

            // 根据模式填充数据
            if (tokenEditMode === 'form') {
                fillTokenForm(key, parsed);
            } else {
                DOMUtils.setValue('tokenValueJson', JSON.stringify(parsed, null, 2));
            }

            TokenButtonManager.setEditMode(true);
            showTokenForm(true);
        }

        // 保存令牌
        async function saveToken() {
            const key = DOMUtils.getValue('tokenKey');
            if (!key) {
                showAlert('请填写令牌标识', 'error');
                return;
            }

            let config;
            if (tokenEditMode === 'form') {
                const formData = collectTokenFormData();
                config = {
                    name: formData.name,
                    channel_keys: formData.channel_keys,
                    total_quota: formData.total_quota
                };

                // 验证必填字段
                if (!FormUtils.validateRequired([
                    { id: 'tokenName', name: '令牌名称', message: '请填写令牌名称' }
                ])) {
                    return;
                }

                if (!formData.total_quota || isNaN(formData.total_quota) || formData.total_quota < 0) {
                    showAlert('请输入有效的总配额数值', 'error');
                    return;
                }
            } else {
                const jsonValue = DOMUtils.getValue('tokenValueJson');
                if (!jsonValue) {
                    showAlert('请填写配置JSON', 'error');
                    return;
                }
                config = FormUtils.validateJSON(jsonValue, 'JSON格式错误，请检查配置格式');
                if (!config) return;
            }

            try {
                await apiRequest(`/api/admin/token/${encodeURIComponent(key)}`, 'POST', config, true, '正在保存令牌...');
                showAlert('令牌保存成功！', 'success');
                clearTokenForm();
                showTokenList();
                loadTokens();
            } catch (error) {
                showAlert(`保存令牌失败: ${error.message}`, 'error');
            }
        }

        // Token functions
        async function loadTokens() {
            try {
                const tokens = await apiRequest('/api/admin/token', 'GET', null, true);
                displayTokens(tokens);
            } catch (error) {
                DOMUtils.getElement('tokensList').innerHTML = `<div class="alert alert-error">加载令牌失败: ${error.message}</div>`;
            }
        }

        async function deleteToken(key) {
            if (!confirm('确定要删除这个令牌吗？')) return;

            try {
                await apiRequest(`/api/admin/token/${encodeURIComponent(key)}`, 'DELETE', null, true, '正在删除令牌...');
                showAlert('令牌删除成功！', 'success');
                loadTokens();
            } catch (error) {
                showAlert(`删除令牌失败: ${error.message}`, 'error');
            }
        }

        // 显示令牌列表的通用函数
        function displayTokens(tokens) {
            const container = DOMUtils.getElement('tokensList');
            if (!container) return;

            // 处理空数据的情况
            if (!tokens || !Array.isArray(tokens) || tokens.length === 0) {
                container.innerHTML = '<div class="alert alert-error">暂无令牌数据</div>';
                return;
            }

            container.innerHTML = tokens.map(item => {
                let parsedValue = {};
                try {
                    parsedValue = JSON.parse(item.value);
                } catch {
                    parsedValue = { name: 'Invalid JSON', channel_keys: ['Invalid JSON'], total_quota: 0 };
                }

                // 处理 channel_keys 显示
                const channelsDisplay = parsedValue.channel_keys && parsedValue.channel_keys.length > 0
                    ? parsedValue.channel_keys.join(', ')
                    : '所有频道';

                // 将配额转换为美元显示
                const totalQuotaValue = parsedValue.total_quota || 0;
                const quotaInDollars = (totalQuotaValue / 1000000).toFixed(2);
                const usageValue = item.usage || 0;
                const usageInDollars = (usageValue / 1000000).toFixed(2);

                const tokenName = parsedValue.name || '未命名';

                return `
                    <div class="item">
                        <div class="item-info">
                            <div class="item-key">${tokenName}</div>
                            <div class="item-details">
                                频道: ${channelsDisplay} | 配额: $${quotaInDollars} | 已用: $${usageInDollars}
                            </div>
                        </div>
                        <div class="actions">
                            <button class="btn btn-secondary" onclick="copyToClipboard('${item.key}')" title="复制token">📋 复制</button>
                            <button class="btn btn-secondary" onclick="editTokenFromList('${item.key}', '${item.value.replace(/'/g, '\\\'').replace(/"/g, '&quot;')}')">✏️ 编辑</button>
                            <button class="btn btn-danger" onclick="deleteToken('${item.key}')">🗑️ 删除</button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // === PRICING FUNCTIONS ===

        // 创建定价编辑模式管理器
        const pricingModeManager = new EditModeManager(
            'pricingTableMode',
            'pricingJsonMode',
            'pricingModeToggle',
            '📊 切换到表格模式',
            '📝 切换到JSON模式'
        );

        // 切换定价编辑模式
        function togglePricingEditMode() {
            const addBtn = DOMUtils.getElement('addPricingModelBtn');

            pricingModeManager.toggle(
                collectPricingTableData,
                (parsed) => displayPricingTable(parsed)
            );

            pricingEditMode = pricingModeManager.getCurrentMode();

            // 控制添加模型按钮的显示
            if (addBtn) {
                if (pricingEditMode === 'table') {
                    DOMUtils.show(addBtn);
                } else {
                    DOMUtils.hide(addBtn);
                }
            }
        }

        // 收集定价表格数据
        function collectPricingTableData() {
            const tbody = DOMUtils.getElement('pricingTableBody');
            const config = {};

            if (!tbody) return config;

            Array.from(tbody.children).forEach((row) => {
                const inputs = row.querySelectorAll('input');
                if (inputs.length < 3) return;

                const modelName = inputs[0].value.trim();
                const inputPrice = parseFloat(inputs[1].value) || 0;
                const outputPrice = parseFloat(inputs[2].value) || 0;

                if (modelName) {
                    config[modelName] = {
                        input: inputPrice,
                        output: outputPrice
                    };
                }
            });

            return config;
        }

        // 添加新的定价模型行
        function addPricingModel() {
            const tbody = DOMUtils.getElement('pricingTableBody');
            if (!tbody) return;

            const rowIndex = tbody.children.length;
            const row = document.createElement('tr');
            row.style.borderBottom = '1px solid #e9ecef';
            row.innerHTML = `
                <td style="padding: 12px; border: 1px solid #e9ecef;">
                    <input type="text" class="form-control" placeholder="例如: gpt-4"
                           onchange="validatePricingRow(${rowIndex})" style="border: none; padding: 8px; background: transparent;">
                </td>
                <td style="padding: 12px; border: 1px solid #e9ecef;">
                    <input type="number" class="form-control" placeholder="0.03" step="0.001" min="0"
                           onchange="validatePricingRow(${rowIndex})" style="border: none; padding: 8px; background: transparent;">
                </td>
                <td style="padding: 12px; border: 1px solid #e9ecef;">
                    <input type="number" class="form-control" placeholder="0.06" step="0.001" min="0"
                           onchange="validatePricingRow(${rowIndex})" style="border: none; padding: 8px; background: transparent;">
                </td>
                <td style="padding: 12px; border: 1px solid #e9ecef; text-align: center;">
                    <button class="btn btn-danger" onclick="removePricingRow(this)" style="font-size: 12px; padding: 6px 12px; white-space: nowrap;">🗑️ 删除</button>
                </td>
            `;
            tbody.appendChild(row);
        }

        // 删除定价模型行
        function removePricingRow(button) {
            const row = button.closest('tr');
            if (row) row.remove();
        }

        // 验证定价行数据
        function validatePricingRow(rowIndex) {
            // 这里可以添加验证逻辑，比如检查模型名称是否重复等
        }

        // Pricing functions
        async function loadPricing() {
            try {
                const pricing = await apiRequest('/api/admin/pricing', 'GET', null, true);
                const addBtn = DOMUtils.getElement('addPricingModelBtn');

                if (pricingEditMode === 'table') {
                    displayPricingTable(pricing);
                    if (addBtn) DOMUtils.show(addBtn);
                } else {
                    DOMUtils.setValue('pricingConfigJson', JSON.stringify(pricing, null, 2));
                    if (addBtn) DOMUtils.hide(addBtn);
                }
            } catch (error) {
                // 失败时显示错误信息并重新创建表格结构
                if (pricingEditMode === 'table') {
                    const pricingContent = DOMUtils.getElement('pricingContent');
                    if (pricingContent) {
                        pricingContent.innerHTML = `
                            <div class="alert alert-error">加载定价配置失败: ${error.message}</div>
                            <div id="pricingTableMode" class="pricing-table">
                                <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                                    <thead>
                                        <tr style="background-color: #f8f9fa; border-bottom: 2px solid #e9ecef;">
                                            <th style="padding: 12px; text-align: left; font-weight: 600; color: #2d3748; border: 1px solid #e9ecef;">模型名称</th>
                                            <th style="padding: 12px; text-align: left; font-weight: 600; color: #2d3748; border: 1px solid #e9ecef;">输入倍率</th>
                                            <th style="padding: 12px; text-align: left; font-weight: 600; color: #2d3748; border: 1px solid #e9ecef;">输出倍率</th>
                                            <th style="padding: 12px; text-align: center; font-weight: 600; color: #2d3748; border: 1px solid #e9ecef; width: 100px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="pricingTableBody">
                                        <!-- 动态生成的定价行 -->
                                    </tbody>
                                </table>
                            </div>
                            <div id="pricingJsonMode" style="display: none;">
                                <div class="form-group">
                                    <label for="pricingConfigJson">定价配置 (JSON)</label>
                                    <textarea id="pricingConfigJson" class="form-control" rows="15"
                                        placeholder='{"gpt-3.5-turbo": {"input": 0.001, "output": 0.002}, "gpt-4": {"input": 0.03, "output": 0.06}}'></textarea>
                                    <small style="color: #666; font-size: 12px; margin-top: 8px; display: block;">
                                        配置格式：模型名称 -> {input: 输入倍率, output: 输出倍率}。倍率基于1百万token=$1.00计算
                                    </small>
                                </div>
                            </div>
                        `;
                    }
                } else {
                    showAlert(`加载定价配置失败: ${error.message}`, 'error');
                }
            }
        }

        // 显示定价配置表格
        function displayPricingTable(pricing) {
            const tbody = DOMUtils.getElement('pricingTableBody');
            if (!tbody) return;

            tbody.innerHTML = '';

            // 如果没有定价数据，添加一行空白行
            if (!pricing || Object.keys(pricing).length === 0) {
                addPricingModel();
                return;
            }

            // 为每个模型创建一行
            Object.entries(pricing).forEach(([modelName, modelPricing]) => {
                const row = document.createElement('tr');
                row.style.borderBottom = '1px solid #e9ecef';

                const inputPrice = modelPricing.input || 0;
                const outputPrice = modelPricing.output || 0;

                row.innerHTML = `
                    <td style="padding: 12px; border: 1px solid #e9ecef;">
                        <input type="text" class="form-control" value="${modelName}"
                               style="border: none; padding: 8px; background: transparent;">
                    </td>
                    <td style="padding: 12px; border: 1px solid #e9ecef;">
                        <input type="number" class="form-control" value="${inputPrice}" step="0.001" min="0"
                               style="border: none; padding: 8px; background: transparent;">
                    </td>
                    <td style="padding: 12px; border: 1px solid #e9ecef;">
                        <input type="number" class="form-control" value="${outputPrice}" step="0.001" min="0"
                               style="border: none; padding: 8px; background: transparent;">
                    </td>
                    <td style="padding: 12px; border: 1px solid #e9ecef; text-align: center;">
                        <button class="btn btn-danger" onclick="removePricingRow(this)" style="font-size: 12px; padding: 6px 12px; white-space: nowrap;">🗑️ 删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 定价验证类
        class PricingValidator {
            static validateTableData() {
                const tbody = DOMUtils.getElement('pricingTableBody');
                if (!tbody) {
                    return { isValid: false, error: '定价表格未找到，请刷新页面重试' };
                }

                const config = {};
                let errorMessage = '';

                // 遍历所有行收集数据
                const rows = Array.from(tbody.children);
                for (let index = 0; index < rows.length; index++) {
                    const row = rows[index];
                    const inputs = row.querySelectorAll('input');
                    if (inputs.length < 3) continue;

                    const modelName = inputs[0].value.trim();
                    const inputPrice = parseFloat(inputs[1].value);
                    const outputPrice = parseFloat(inputs[2].value);

                    // 验证数据
                    if (!modelName) {
                        errorMessage = `第 ${index + 1} 行：模型名称不能为空`;
                        break;
                    }

                    if (isNaN(inputPrice) || inputPrice < 0) {
                        errorMessage = `第 ${index + 1} 行：输入倍率必须是有效的非负数`;
                        break;
                    }

                    if (isNaN(outputPrice) || outputPrice < 0) {
                        errorMessage = `第 ${index + 1} 行：输出倍率必须是有效的非负数`;
                        break;
                    }

                    // 检查模型名称是否重复
                    if (config[modelName]) {
                        errorMessage = `模型名称 "${modelName}" 重复`;
                        break;
                    }

                    config[modelName] = {
                        input: inputPrice,
                        output: outputPrice
                    };
                }

                if (errorMessage) {
                    return { isValid: false, error: errorMessage };
                }

                if (Object.keys(config).length === 0) {
                    return { isValid: false, error: '请至少添加一个模型的定价配置' };
                }

                return { isValid: true, data: config };
            }
        }

        async function updatePricing() {
            let config;

            if (pricingEditMode === 'table') {
                const validation = PricingValidator.validateTableData();
                if (!validation.isValid) {
                    showAlert(validation.error, 'error');
                    return;
                }
                config = validation.data;
            } else {
                const configText = DOMUtils.getValue('pricingConfigJson');
                if (!configText) {
                    showAlert('请填写定价配置', 'error');
                    return;
                }
                config = FormUtils.validateJSON(configText, '配置格式错误，请检查JSON格式');
                if (!config) return;
            }

            try {
                await apiRequest('/api/admin/pricing', 'POST', config, true, '正在更新定价配置...');
                showAlert('定价配置更新成功！', 'success');
                loadPricing();
            } catch (error) {
                showAlert(`更新定价配置失败: ${error.message}`, 'error');
            }
        }

        // === API TEST FUNCTIONS ===

        // API测试管理类
        class ApiTestManager {
            static async sendRequest() {
                const token = DOMUtils.getValue('apiTestToken');
                const requestText = DOMUtils.getValue('apiTestRequest');
                const responseTextarea = DOMUtils.getElement('apiTestResponse');
                const statusDiv = DOMUtils.getElement('apiTestStatus');

                if (!FormUtils.validateRequired([
                    { id: 'apiTestToken', name: 'Bearer Token', message: '请输入 Bearer Token' },
                    { id: 'apiTestRequest', name: '请求 JSON', message: '请输入请求 JSON' }
                ])) {
                    return;
                }

                const requestJson = FormUtils.validateJSON(requestText, '请求 JSON 格式错误');
                if (!requestJson) return;

                // 显示加载状态
                if (responseTextarea) responseTextarea.value = '正在发送请求...';
                if (statusDiv) statusDiv.style.display = 'none';

                try {
                    const startTime = Date.now();

                    const response = await fetch('/v1/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify(requestJson)
                    });

                    const endTime = Date.now();
                    const duration = endTime - startTime;

                    this._displayTestResult(response, duration, responseTextarea, statusDiv);
                } catch (error) {
                    this._displayTestError(error, responseTextarea, statusDiv);
                }
            }

            static async _displayTestResult(response, duration, responseTextarea, statusDiv) {
                if (statusDiv) statusDiv.style.display = 'block';

                if (response.ok) {
                    const responseData = await response.json();
                    if (responseTextarea) {
                        responseTextarea.value = JSON.stringify(responseData, null, 2);
                    }

                    if (statusDiv) {
                        statusDiv.className = 'alert alert-success';
                        statusDiv.innerHTML = `
                            <strong>✅ 请求成功</strong><br>
                            状态码: ${response.status}<br>
                            响应时间: ${duration}ms
                        `;
                    }
                } else {
                    const errorText = await response.text();
                    if (responseTextarea) responseTextarea.value = errorText;

                    if (statusDiv) {
                        statusDiv.className = 'alert alert-error';
                        statusDiv.innerHTML = `
                            <strong>❌ 请求失败</strong><br>
                            状态码: ${response.status}<br>
                            响应时间: ${duration}ms
                        `;
                    }
                }
            }

            static _displayTestError(error, responseTextarea, statusDiv) {
                if (responseTextarea) {
                    responseTextarea.value = `请求失败: ${error.message}`;
                }

                if (statusDiv) {
                    statusDiv.style.display = 'block';
                    statusDiv.className = 'alert alert-error';
                    statusDiv.innerHTML = `<strong>❌ 网络错误</strong><br>${error.message}`;
                }
            }

            static clear() {
                DOMUtils.clearInput('apiTestToken');
                DOMUtils.setValue('apiTestRequest', `{
  "model": "gpt-4",
  "messages": [
    {
      "role": "user",
      "content": "Hello, world!"
    }
  ]
}`);
                DOMUtils.clearInput('apiTestResponse');
                const statusDiv = DOMUtils.getElement('apiTestStatus');
                if (statusDiv) statusDiv.style.display = 'none';
            }
        }

        // 发送 API 测试请求
        async function sendApiTest() {
            await ApiTestManager.sendRequest();
        }

        // 清空 API 测试数据
        function clearApiTest() {
            ApiTestManager.clear();
        }

        // === UTILITY FUNCTIONS ===

        // 剪贴板管理类
        class ClipboardManager {
            static async copy(text) {
                try {
                    await navigator.clipboard.writeText(text);
                    showAlert('已复制到剪贴板', 'success', 2000);
                } catch (error) {
                    // 降级方案
                    this._fallbackCopy(text);
                }
            }

            static _fallbackCopy(text) {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showAlert('已复制到剪贴板', 'success', 2000);
            }
        }

        function copyToClipboard(text) {
            ClipboardManager.copy(text);
        }

        // 消息提示管理类
        class AlertManager {
            static show(message, type, duration = 5000) {
                // 移除现有提示
                this._removeExistingAlerts();

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-top`;
                alertDiv.textContent = message;

                const container = document.querySelector('.container');
                if (container) {
                    container.insertBefore(alertDiv, container.firstChild);
                }

                // 自动移除
                setTimeout(() => {
                    alertDiv.remove();
                }, duration);
            }

            static _removeExistingAlerts() {
                const existingAlerts = document.querySelectorAll('.alert');
                existingAlerts.forEach(alert => alert.remove());
            }
        }

        function showAlert(message, type, duration = 5000) {
            AlertManager.show(message, type, duration);
        }

        // Loading 管理对象
        const LoadingManager = {
            delayTimer: null,
            minShowTimer: null,
            loadingElement: null,
            showStartTime: null,
            showDelay: 500, // 延迟 500ms 显示，避免快速请求的闪烁
            minShowDuration: 500, // 最小显示时长 500ms，避免一闪而过

            show(message) {
                // 如果已存在 loading，先清理
                this.hide();

                // 设置延迟显示
                this.delayTimer = setTimeout(() => {
                    this._showLoading(message);
                }, this.showDelay);
            },

            _showLoading(message) {
                const mainContent = document.getElementById('mainContent');
                if (!mainContent) return;

                // 记录显示开始时间
                this.showStartTime = Date.now();

                // 添加 loading 状态类
                mainContent.classList.add('loading-active');

                // 创建 loading 元素
                const loadingDiv = document.createElement('div');
                loadingDiv.id = 'loadingOverlay';
                loadingDiv.innerHTML = `
                    <div class="spinner"></div>
                    <p>${message}</p>
                `;

                // 初始设置为透明，准备淡入动画
                loadingDiv.style.opacity = '0';
                mainContent.appendChild(loadingDiv);

                this.loadingElement = loadingDiv;

                // 触发淡入动画
                requestAnimationFrame(() => {
                    loadingDiv.style.opacity = '1';
                });
            },

            hide() {
                // 清除延迟显示定时器
                if (this.delayTimer) {
                    clearTimeout(this.delayTimer);
                    this.delayTimer = null;
                }

                // 如果 loading 还没有显示，直接返回
                if (!this.loadingElement) {
                    return;
                }

                // 计算已显示时长
                const showDuration = Date.now() - this.showStartTime;
                const remainingMinShowTime = Math.max(0, this.minShowDuration - showDuration);

                // 如果还没达到最小显示时长，延迟隐藏
                if (remainingMinShowTime > 0) {
                    this.minShowTimer = setTimeout(() => {
                        this._hideLoading();
                    }, remainingMinShowTime);
                } else {
                    this._hideLoading();
                }
            },

            _hideLoading() {
                // 清除最小显示定时器
                if (this.minShowTimer) {
                    clearTimeout(this.minShowTimer);
                    this.minShowTimer = null;
                }

                if (this.loadingElement) {
                    // 淡出动画
                    this.loadingElement.style.opacity = '0';

                    // 等待动画完成后移除元素
                    setTimeout(() => {
                        if (this.loadingElement) {
                            this.loadingElement.remove();
                            this.loadingElement = null;
                        }

                        // 移除 loading 状态类
                        const mainContent = document.getElementById('mainContent');
                        if (mainContent) {
                            mainContent.classList.remove('loading-active');
                        }
                    }, 300); // 与 CSS transition 时间匹配
                }

                this.showStartTime = null;
            }
        };

        // 保持原有的函数接口，以兼容现有代码
        function showLoading(message) {
            LoadingManager.show(message);
        }

        function hideLoading() {
            LoadingManager.hide();
        }

        // === 页面初始化和事件处理 ===

        // 页面初始化管理类
        class PageInitializer {
            static init() {
                this._initAuth();
                this._initEventListeners();
                this._initKeyboardShortcuts();
            }

            static _initAuth() {
                if (AuthManager.restoreToken()) {
                    showMainContent();
                } else {
                    showHomePage();
                }
            }

            static _initEventListeners() {
                // 为登录输入框添加回车键监听
                const tokenInput = DOMUtils.getElement('adminToken');
                if (tokenInput) {
                    tokenInput.addEventListener('keypress', (event) => {
                        if (event.key === 'Enter') {
                            authenticate();
                        }
                    });
                }

                // 点击模态框外部关闭
                window.onclick = (event) => {
                    const modal = DOMUtils.getElement('loginModal');
                    if (event.target === modal) {
                        closeLoginModal();
                    }
                };
            }

            static _initKeyboardShortcuts() {
                // ESC键关闭模态框
                document.addEventListener('keydown', (event) => {
                    if (event.key === 'Escape') {
                        const modal = DOMUtils.getElement('loginModal');
                        if (modal && modal.style.display === 'block') {
                            closeLoginModal();
                        }
                    }
                });
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            PageInitializer.init();
        });
    </script>
</body>

</html>
