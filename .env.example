# Application settings
DEBUG=true
HOST=0.0.0.0
PORT=8000

# Security
ADMIN_TOKEN=your-secure-admin-token-here
SECRET_KEY=your-secret-key-here

# Database
DATABASE_URL=sqlite:///./oneapi.db
# For PostgreSQL: postgresql://user:password@localhost/dbname
# For MySQL: mysql://user:password@localhost/dbname

# CORS
CORS_ORIGINS=["*"]

# Rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Logging
LOG_LEVEL=INFO

# Provider settings
REQUEST_TIMEOUT=30
MAX_RETRIES=3