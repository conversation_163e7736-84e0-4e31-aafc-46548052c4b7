# One API - CFastAPI Version

基于 **CFastAPI** 的多提供商 AI API 代理服务，专为 **Cloudflare Workers** 设计，使用 FastAPI 风格的现代 Python 开发体验。

## 🚀 特性

- **FastAPI 风格**: 装饰器路由、依赖注入、数据验证
- **Cloudflare Workers**: 完全兼容 Cloudflare Workers Python 环境
- **多提供商支持**: OpenAI、Azure OpenAI 等
- **类型安全**: 使用 dataclass 和类型提示
- **自动文档**: 基于 OpenAPI 的 API 文档
- **依赖注入**: 清晰的服务层架构

## 📁 项目结构

```
src/
├── cfastapi/           # CFastAPI 框架核心
│   ├── app.py         # 主应用类
│   ├── routing.py     # 路由系统
│   ├── depends.py     # 依赖注入
│   ├── middleware.py  # 中间件
│   └── exceptions.py  # 异常处理
├── api/               # API 路由
│   ├── v1/           # v1 API (chat, completions, models)
│   └── admin/        # 管理 API (channels, tokens, settings)
├── schemas/          # Pydantic 数据模型
├── services/         # 依赖注入服务
└── providers/        # AI 提供商处理

main.py              # CFastAPI 应用入口
wrangler.toml        # Cloudflare 配置
```

## 🚀 快速开始

### 1. 配置 Cloudflare Workers

编辑 `wrangler.toml`：
```toml
name = "one-api-py"
main = "main.py"
compatibility_date = "2025-04-28"
compatibility_flags = ["python_workers"]

[vars]
ADMIN_TOKEN = "your-secure-admin-token"

[[d1_databases]]
binding = "DB"
database_name = "your-database-name"
database_id = "your-database-id"

[assets]
directory = "public"
binding = "ASSETS"
```

### 2. 部署到 Cloudflare Workers

```bash
# 创建 D1 数据库
npx wrangler d1 create one-api-db

# 部署应用
npx wrangler deploy
```

### 3. 访问应用

- **API 文档**: `https://your-worker.workers.dev/api/docs`
- **管理界面**: `https://your-worker.workers.dev/`
- **健康检查**: `https://your-worker.workers.dev/health`

## 🔧 API 使用

### Chat Completion
```bash
curl -X POST "https://your-worker.workers.dev/v1/chat/completions" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

### 管理 API
```bash
# 获取渠道列表
curl -X GET "https://your-worker.workers.dev/api/admin/channels" \
  -H "Authorization: Bearer your-admin-token"

# 创建 Token
curl -X POST "https://your-worker.workers.dev/api/admin/tokens" \
  -H "Authorization: Bearer your-admin-token" \
  -H "Content-Type: application/json" \
  -d '{"name": "My Token", "total_quota": 10.0}'
```

## 🏗️ CFastAPI 框架特性

### 装饰器路由
```python
from src.cfastapi.routing import Router
from src.schemas.chat import ChatCompletionRequest, ChatCompletionResponse

router = Router()

@router.post("/completions", response_model=ChatCompletionResponse)
async def create_completion(
    request_data: ChatCompletionRequest,
    token: ApiTokenData = Depends(get_current_token)
):
    return process_completion(request_data, token)
```

### 依赖注入
```python
from src.cfastapi import Depends

async def get_database(app) -> DatabaseManager:
    env = app.state.get('env')
    return DatabaseManager(env.DB)

@router.get("/data")
async def get_data(db: DatabaseManager = Depends(get_database)):
    return await db.get_all_channels()
```

### 数据验证
```python
from dataclasses import dataclass
from src.schemas.common import validate_chat_request

@dataclass
class ChatRequest:
    model: str
    messages: List[Dict[str, Any]]
    temperature: float = 1.0

@router.post("/chat")
async def chat(request_data: Dict[str, Any]):  # 自动验证
    validated_data = validate_chat_request(request_data)
    return process_chat(validated_data)
```

## 📚 API 文档

访问 `/api/docs` 查看完整的 Swagger UI 文档，包括：

- **v1 API**: 
  - `POST /v1/chat/completions` - 聊天完成
  - `POST /v1/completions` - 文本完成
  - `GET /v1/models` - 列出模型

- **管理 API**:
  - `/api/admin/channels` - 渠道管理
  - `/api/admin/tokens` - Token 管理
  - `/api/admin/settings` - 设置管理
  - `/api/admin/usage` - 使用统计

## ⚙️ 配置管理

### 添加 OpenAI 渠道
```json
{
  "key": "openai-main",
  "name": "OpenAI Main",
  "type": "openai",
  "endpoint": "https://api.openai.com",
  "api_key": "sk-your-key",
  "deployment_mapper": {
    "gpt-4": "gpt-4",
    "gpt-3.5-turbo": "gpt-3.5-turbo"
  }
}
```

### 添加 Azure OpenAI 渠道
```json
{
  "key": "azure-openai",
  "name": "Azure OpenAI",
  "type": "azure-openai",
  "endpoint": "https://your-resource.openai.azure.com",
  "api_key": "your-azure-key",
  "api_version": "2024-02-15-preview",
  "deployment_mapper": {
    "gpt-4": "gpt-4-deployment",
    "gpt-35-turbo": "gpt-35-turbo-deployment"
  }
}
```

## 🔧 开发指南

### 添加新端点
```python
# 1. 定义数据结构
@dataclass
class NewRequest:
    param1: str
    param2: Optional[int] = None
    
    def to_dict(self):
        return {"param1": self.param1, "param2": self.param2}

# 2. 创建路由
@router.post("/new")
async def new_endpoint(
    request_data: Dict[str, Any],
    token: ApiTokenData = Depends(get_current_token)
):
    return {"result": "success"}
```

### 自定义依赖
```python
async def get_custom_service(app) -> CustomService:
    env = app.state.get('env')
    return CustomService(env)

@router.get("/custom")
async def custom_endpoint(
    service: CustomService = Depends(get_custom_service)
):
    return await service.process()
```

## 🚀 部署和运维

### 生产环境配置
```toml
# wrangler.toml
[vars]
ADMIN_TOKEN = "super-secure-admin-token"

[python]
version = "3.12"

[observability.logs]
enabled = true
```

### 监控和日志
- 使用 Cloudflare Workers 日志查看请求
- 通过 `/health` 端点监控健康状态
- 管理界面查看使用统计

## 🔄 迁移说明

如果您从其他版本迁移：

1. **保持配置不变**: `wrangler.toml` 无需修改
2. **API 完全兼容**: 现有客户端代码无需改动
3. **数据库兼容**: 自动迁移现有数据
4. **功能增强**: 新增类型安全和自动文档

## 📞 支持

- **问题反馈**: 创建 GitHub Issue
- **API 文档**: `/api/docs` 
- **健康检查**: `/health`

## 📄 许可证

MIT License

---

**CFastAPI** - 为 Cloudflare Workers 设计的 FastAPI 风格框架，提供现代 Python 开发体验！