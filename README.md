# AWSL One API - Python Implementation

一个基于 Cloudflare Workers Python 的 OpenAI API 代理服务，支持多渠道管理、Token 管理和使用量统计。

## ✨ 特性

- 🚀 **基于 Cloudflare Workers Python**：无服务器架构，全球边缘部署
- 🔐 **多渠道支持**：支持 Azure OpenAI 和 OpenAI，后续可继续增加其他 AI 服务提供商
- 🎫 **Token 管理**：完整的 API Token 生成、管理和配额控制
- 📊 **使用量统计**：实时统计 API 使用量和费用
- 💰 **定价管理**：灵活的模型定价配置
- 🎨 **Web 管理界面**：直观的 Web 界面进行配置管理
- 🧪 **API 测试工具**：内置 API 测试功能，支持实时调试
- 📚 **OpenAPI 兼容**：完全兼容 OpenAI API 格式

## 🏗️ 项目结构

```text
awsl-one-api-python/
├── src/                          # 源代码目录
│   ├── admin/                    # 管理接口
│   │   ├── admin_api.py          # Admin API 实现
│   │   └── __init__.py           # Admin 模块入口
│   ├── providers/                # AI 服务提供商
│   │   ├── base_provider.py      # 基础提供商类
│   │   ├── openai_provider.py    # OpenAI 代理实现
│   │   ├── azure_openai_provider.py  # Azure OpenAI 代理实现
│   │   └── __init__.py           # 提供商模块入口
│   ├── database.py               # 数据库操作
│   ├── models.py                 # 数据模型定义
│   ├── constants.py              # 常量定义
│   ├── utils.py                  # 工具函数
│   └── __init__.py               # 源码模块入口
├── public/                       # 静态文件
│   └── index.html                # Web 管理界面
├── main.py                       # 主入口文件
├── wrangler.toml                 # Cloudflare Workers 配置
├── wrangler.toml.template        # 配置模板
├── package.json                  # 项目配置
├── requirements.txt              # Python 依赖
└── README.md                     # 项目文档
```

## 🏗️ 系统架构

```text
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Client Apps    │───▶│  AWSL One API   │───▶│  AI Providers   │
│                 │    │ (Python Workers)│    │(Azure OpenAI &  │
└─────────────────┘    └─────────────────┘    │    OpenAI)      │
                              │                └─────────────────┘
                              ▼
                       ┌─────────────────┐
                       │  Cloudflare D1  │
                       │    Database     │
                       └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- Node.js 18+
- pnpm 或 npm
- Cloudflare Workers 账户
- Python 3.12 (Cloudflare Workers Python 运行时)

### 安装依赖

```bash
pnpm install
```

### 配置环境

1. 复制 `wrangler.toml.template` 为 `wrangler.toml` 并修改配置：

```toml
name = "awsl-one-api-python"
main = "main.py"
compatibility_date = "2025-04-28"
compatibility_flags = ["python_workers"]

routes = [
    { pattern = "your-domain.com", custom_domain = true },
]

[vars]
ADMIN_TOKEN = "your-secure-admin-token-here"

[assets]
directory = "public"
binding = "ASSETS"
run_worker_first = true

[[d1_databases]]
binding = "DB"
database_name = "your-database-name"
database_id = "your-database-id"

[python]
version = "3.12"
```

2. 创建 Cloudflare D1 数据库：

```bash
pnpm run db:create
```

### 本地开发

```bash
pnpm dev
```

### 部署到生产环境

```bash
pnpm run deploy
```

## 📖 使用指南

### 初始化数据库

首次部署后，需要通过 Web 界面或 API 初始化数据库：

```bash
# 通过 API 初始化
curl -X POST https://your-domain.com/api/admin/db_initialize \
  -H "Authorization: Bearer your-admin-token"
```

或访问 Web 管理界面：
1. 访问 `https://your-domain.com`
2. 使用管理员 Token 登录
3. 切换到 **📊 数据库** 标签
4. 点击 **🔄 初始化数据库** 按钮

### 频道配置

#### 通过 Web 界面：
1. 在 Web 界面切换到 **🔗 频道管理** 标签
2. 点击 **➕ 添加频道** 按钮
3. 选择频道类型（Azure OpenAI 或 OpenAI）
4. 填写频道配置信息
5. 点击 **💾 保存频道** 按钮

#### 通过 API：
```bash
# 创建 OpenAI 频道
curl -X POST https://your-domain.com/api/admin/channel \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-admin-token" \
  -d '{
    "name": "My OpenAI Channel",
    "type": "openai",
    "endpoint": "https://api.openai.com/v1/",
    "api_key": "sk-your-openai-api-key",
    "deployment_mapper": {
      "gpt-4": "gpt-4",
      "gpt-3.5-turbo": "gpt-3.5-turbo"
    }
  }'

# 创建 Azure OpenAI 频道
curl -X POST https://your-domain.com/api/admin/channel \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-admin-token" \
  -d '{
    "name": "My Azure OpenAI",
    "type": "azure-openai",
    "endpoint": "https://your-resource.openai.azure.com/",
    "api_key": "your-azure-api-key",
    "api_version": "2024-02-15-preview",
    "deployment_mapper": {
      "gpt-4": "gpt-4-deployment-name",
      "gpt-3.5-turbo": "gpt-35-turbo-deployment-name"
    }
  }'
```

### Token 创建和使用

#### 创建 Token：
```bash
curl -X POST https://your-domain.com/api/admin/token \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-admin-token" \
  -d '{
    "name": "Development Token",
    "channel_keys": [],
    "total_quota": 100.0
  }'
```

#### 使用 Token 调用 API：
```bash
curl https://your-domain.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {
        "role": "user",
        "content": "Hello, world!"
      }
    ]
  }'
```

## 🛠️ 管理功能

### Web 管理界面

访问 `https://your-domain.com` 即可使用 Web 管理界面，功能包括：

- **📊 数据库管理**：一键初始化数据库表结构
- **🔗 频道配置管理**：添加、编辑、删除 AI 服务提供商频道
- **🔑 API Token 管理**：生成、管理和监控 API Token 使用情况
- **💰 定价配置**：灵活配置不同模型的定价策略
- **🧪 API 测试工具**：内置 API 测试界面，支持实时调试

### API 管理接口

| 端点 | 方法 | 描述 |
|------|------|------|
| `/api/admin/db_initialize` | POST | 初始化数据库 |
| `/api/admin/channel` | GET | 获取所有频道 |
| `/api/admin/channel` | POST | 创建新频道 |
| `/api/admin/channel/{key}` | GET | 获取指定频道 |
| `/api/admin/channel/{key}` | PUT | 更新频道 |
| `/api/admin/channel/{key}` | DELETE | 删除频道 |
| `/api/admin/token` | GET | 获取所有 Token |
| `/api/admin/token` | POST | 创建新 Token |
| `/api/admin/token/{token}` | GET | 获取指定 Token |
| `/api/admin/token/{token}` | PUT | 更新 Token |
| `/api/admin/token/{token}` | DELETE | 删除 Token |
| `/api/admin/pricing` | GET | 获取全局定价 |
| `/api/admin/pricing` | POST | 更新全局定价 |

## 🔧 配置说明

### 频道配置格式

#### OpenAI 配置：
```python
{
    "name": "My OpenAI Channel",
    "type": "openai",
    "endpoint": "https://api.openai.com/v1/",
    "api_key": "sk-your-openai-api-key",
    "deployment_mapper": {
        "gpt-4": "gpt-4",
        "gpt-3.5-turbo": "gpt-3.5-turbo"
    },
    "model_pricing": {  # 可选：自定义定价
        "gpt-4": {"input": 0.03, "output": 0.06}
    }
}
```

#### Azure OpenAI 配置：
```python
{
    "name": "My Azure OpenAI",
    "type": "azure-openai",
    "endpoint": "https://your-resource.openai.azure.com/",
    "api_key": "your-azure-api-key",
    "api_version": "2024-02-15-preview",
    "deployment_mapper": {
        "gpt-4": "gpt-4-deployment-name",
        "gpt-3.5-turbo": "gpt-35-turbo-deployment-name"
    }
}
```

### Token 配置格式

```python
{
    "name": "用户令牌1",
    "channel_keys": ["channel-1", "channel-2"],  # 空数组表示访问所有频道
    "total_quota": 1000.0,  # 总配额（美元）
    "enabled": True  # 是否启用
}
```

## 🆕 与 TypeScript 版本的差异

1. **运行时**：使用 Cloudflare Workers Python 运行时而非 TypeScript
2. **语言特性**：利用 Python 的数据类和类型提示
3. **模块化**：采用 Python 的模块和包结构
4. **配置**：增加了 Python Workers 特定的配置项
5. **依赖管理**：使用 requirements.txt（虽然 Workers 环境下依赖有限）

## 📊 监控与统计

- **使用量统计**：自动记录每次 API 调用的 Token 使用量
- **费用计算**：基于模型定价自动计算费用
- **配额管理**：支持 Token 级别的配额限制
- **实时监控**：Web 界面实时显示使用情况和剩余配额

## 🎯 核心优势

- **零配置部署**：基于 Cloudflare Workers，无需服务器维护
- **全球加速**：利用 Cloudflare 全球边缘网络，低延迟访问
- **成本优化**：按需计费，无固定服务器成本
- **高可用性**：Cloudflare 基础设施保证 99.9% 可用性
- **安全可靠**：内置 Token 认证和配额管理机制
- **Python 生态**：利用 Python 的简洁语法和丰富的数据处理能力

## 🔒 安全性

- **Token 认证**：所有 API 调用需要有效的 Bearer Token
- **管理员认证**：管理接口使用独立的管理员 Token
- **CORS 支持**：配置跨域访问策略
- **数据加密**：Cloudflare D1 提供数据加密存储

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 🙋‍♂️ 支持

如有问题或建议，请创建 Issue 或联系维护者。