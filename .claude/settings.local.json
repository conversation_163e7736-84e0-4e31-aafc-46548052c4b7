{"permissions": {"allow": ["Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "<PERSON><PERSON>(python:*)", "Bash(ls:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(grep:*)", "<PERSON><PERSON>(wrangler d1 list:*)", "WebFetch(domain:chanfana.pages.dev)", "WebFetch(domain:github.com)", "WebFetch(domain:www.npmjs.com)", "Bash(pnpm dev:*)", "Bash(npm run dev:*)", "Bash(npm install)", "mcp__sse-server__accounts_list", "mcp__sse-server__set_active_account", "mcp__sse-server__workers_list", "<PERSON><PERSON>(wrangler deploy:*)", "Bash(rg:*)", "Bash(git rebase:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(chmod:*)", "Bash(git rm:*)", "<PERSON><PERSON>(npx wrangler:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(echo:*)"], "deny": []}}