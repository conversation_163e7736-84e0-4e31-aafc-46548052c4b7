"""
AWSL One API - Python implementation for Cloudflare Workers
A multi-provider AI API proxy with management capabilities
"""

from workers import Response
from js import fetch, URL
import json
import asyncio
import traceback
from typing import Dict, Any, Optional, List
import re
import random

from src.database import DatabaseManager
from src.models import ChannelConfig, ApiTokenData, Usage
from src.providers import get_provider_handler
from src.admin import AdminAPI
from src.constants import API_PATHS, ADMIN_TOKEN_HEADER
from src.utils import parse_json_safe, get_request_json, create_response, create_html_response
from src.openapi_spec import get_openapi_json
from src.docs_templates import get_swagger_ui_html, get_redoc_html


class OneAPIApp:
    def __init__(self, env: Dict[str, Any]):
        self.env = env
        
        # Debug env object
        print(f"Env type: {type(env)}")
        
        # Handle env object differences in Cloudflare Workers Python
        try:
            # Try direct attribute access first
            db_binding = env.DB if hasattr(env, 'DB') else None
            admin_token = env.ADMIN_TOKEN if hasattr(env, 'ADMIN_TOKEN') else None
        except Exception as e:
            print(f"Error accessing env attributes: {e}")
            # Fallback to dictionary access
            db_binding = env.get('DB') if hasattr(env, 'get') else None
            admin_token = env.get('ADMIN_TOKEN') if hasattr(env, 'get') else None
        
        print(f"DB binding: {db_binding}")
        print(f"Admin token present: {admin_token is not None}")
        
        self.db = DatabaseManager(db_binding)
        self.admin_api = AdminAPI(self.db, admin_token)
        
    async def handle_request(self, request) -> Response:
        """Main request handler"""
        try:
            url = URL.new(request.url)
            path = url.pathname
            method = request.method
            
            # Log request details for debugging
            print(f"Request: {method} {path}")
            
            # Serve static files for admin panel
            assets_binding = getattr(self.env, 'ASSETS', None) if hasattr(self.env, 'ASSETS') else self.env.get('ASSETS') if hasattr(self.env, 'get') else None
            if not self._is_api_path(path) and assets_binding:
                return await self._serve_static(request)
            
            # Handle API documentation routes
            if path == '/api/docs':
                return self._serve_swagger_ui()
            elif path == '/api/redocs':
                return self._serve_redoc()
            elif path == '/api/openapi.json':
                return self._serve_openapi_spec()
            
            # Handle admin API routes
            if path.startswith('/api/admin/'):
                return await self.admin_api.handle_request(request)
            
            # Handle provider API routes
            if path.startswith('/v1/'):
                return await self._handle_provider_request(request)
            
            # Default response
            return create_response({"message": "AWSL One API"}, 200)
            
        except Exception as e:
            print(f"Error in handle_request: {str(e)}")
            print(f"Error type: {type(e).__name__}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            return create_response({"error": str(e), "type": type(e).__name__}, 500)
    
    def _is_api_path(self, path: str) -> bool:
        """Check if path is an API endpoint"""
        return any(path.startswith(api_path) for api_path in API_PATHS)
    
    async def _serve_static(self, request) -> Response:
        """Serve static files from ASSETS binding"""
        url = URL.new(request.url)
        if '.' not in url.pathname:
            url.pathname = ""
        assets_binding = getattr(self.env, 'ASSETS', None) if hasattr(self.env, 'ASSETS') else self.env.get('ASSETS') if hasattr(self.env, 'get') else None
        if assets_binding:
            return await assets_binding.fetch(url)
        else:
            return create_response({"error": "Assets not available"}, 404)
    
    def _serve_swagger_ui(self) -> Response:
        """Serve Swagger UI documentation page"""
        html_content = get_swagger_ui_html()
        return create_html_response(html_content)
    
    def _serve_redoc(self) -> Response:
        """Serve ReDoc documentation page"""
        html_content = get_redoc_html()
        return create_html_response(html_content)
    
    def _serve_openapi_spec(self) -> Response:
        """Serve OpenAPI JSON specification"""
        openapi_json = get_openapi_json()
        return create_response(openapi_json, 200, {
            "Content-Type": "application/json; charset=utf-8"
        })
    
    async def _handle_provider_request(self, request) -> Response:
        """Handle AI provider API requests"""
        try:
            # Extract and validate API token
            auth_header = request.headers.get('Authorization', '')
            if not auth_header.startswith('Bearer '):
                return create_response({"error": "Invalid authorization header"}, 401)
            
            token = auth_header[7:]  # Remove 'Bearer ' prefix
            
            # Validate token and get configuration
            token_data = await self.db.get_api_token(token)
            if not token_data:
                return create_response({"error": "Invalid API token"}, 401)
            
            # Parse request body
            request_data = await get_request_json(request)
            if not request_data:
                return create_response({"error": "Invalid JSON in request body"}, 400)
            
            model = request_data.get('model')
            if not model:
                return create_response({"error": "Model parameter is required"}, 400)
            
            # Get available channels for this token and model
            channels = await self._get_available_channels(token_data, model)
            if not channels:
                return create_response({"error": f"No available channels for model: {model}"}, 404)
            
            # Select a channel (simple random selection)
            selected_channel = random.choice(channels)
            
            # Get provider handler and make request
            provider_handler = get_provider_handler(selected_channel['type'])
            if not provider_handler:
                return create_response({"error": f"Unsupported provider type: {selected_channel['type']}"}, 500)
            
            # Make provider request
            provider_response = await provider_handler.make_request(
                selected_channel, request_data, request
            )
            
            # Track usage if successful
            if provider_response.status == 200:
                usage_data = await self._extract_usage_from_response(provider_response)
                if usage_data:
                    await self._update_token_usage(token, usage_data, selected_channel, model)
            
            return provider_response
            
        except Exception as e:
            print(f"Error in provider request: {str(e)}")
            return create_response({"error": "Internal server error"}, 500)
    
    async def _get_available_channels(self, token_data: ApiTokenData, model: str) -> List[Dict[str, Any]]:
        """Get channels available for token and model"""
        all_channels = await self.db.get_all_channels()
        
        # Filter by token permissions
        if token_data.channel_keys:
            allowed_channels = {key: config for key, config in all_channels.items() 
                             if key in token_data.channel_keys}
        else:
            allowed_channels = all_channels
        
        # Filter by model support
        available_channels = []
        for key, config in allowed_channels.items():
            if self._channel_supports_model(config, model):
                channel_data = config.copy()
                channel_data['key'] = key
                available_channels.append(channel_data)
        
        return available_channels
    
    def _channel_supports_model(self, channel_config: ChannelConfig, model: str) -> bool:
        """Check if channel supports the requested model"""
        deployment_mapper = channel_config.get('deployment_mapper', {})
        return model in deployment_mapper
    
    async def _extract_usage_from_response(self, response: Response) -> Optional[Usage]:
        """Extract token usage from provider response"""
        try:
            response_data = await response.json()
            usage = response_data.get('usage', {})
            
            if usage:
                return Usage(
                    prompt_tokens=usage.get('prompt_tokens', 0),
                    completion_tokens=usage.get('completion_tokens', 0),
                    total_tokens=usage.get('total_tokens', 0)
                )
        except:
            pass
        return None
    
    async def _update_token_usage(self, token: str, usage: Usage, channel: Dict[str, Any], model: str):
        """Update token usage and calculate cost"""
        try:
            # Get pricing for the model
            pricing = await self._get_model_pricing(channel, model)
            if not pricing:
                return
            
            # Calculate cost
            input_cost = (usage.prompt_tokens / 1000) * pricing.get('input', 0)
            output_cost = (usage.completion_tokens / 1000) * pricing.get('output', 0)
            total_cost = input_cost + output_cost
            
            # Update token usage
            await self.db.update_token_usage(token, total_cost)
            
        except Exception as e:
            print(f"Error updating token usage: {str(e)}")
    
    async def _get_model_pricing(self, channel: Dict[str, Any], model: str) -> Optional[Dict[str, float]]:
        """Get pricing for model from channel or global settings"""
        # Check channel-specific pricing first
        channel_pricing = channel.get('model_pricing', {})
        if model in channel_pricing:
            return channel_pricing[model]
        
        # Fall back to global pricing
        global_pricing = await self.db.get_setting('global_pricing', {})
        return global_pricing.get(model)


# Cloudflare Workers Python entry point
async def on_fetch(request, env, context=None):
    """Main entry point for Cloudflare Workers Python runtime"""
    try:
        app = OneAPIApp(env)
        return await app.handle_request(request)
    except Exception as e:
        error_details = {
            "error": str(e),
            "type": type(e).__name__,
            "traceback": traceback.format_exc()
        }
        return Response(
            json.dumps(error_details, ensure_ascii=False), 
            status=500,
            headers={
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*"
            }
        )