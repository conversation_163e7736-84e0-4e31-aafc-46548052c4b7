"""
AWSL One API - CFastAPI implementation for Cloudflare Workers
A multi-provider AI API proxy with management capabilities using FastAPI-style architecture
"""

import json
import traceback
from typing import Dict, Any
from workers import Response
from js import URL

from src.cfastapi import CFastAPI, CORSMiddleware
from src.api.v1 import v1_router
from src.api.admin import admin_router
from src.database_simple import SimpleDatabaseManager as DatabaseManager
from src.utils import create_response, create_html_response
from src.openapi_spec import get_openapi_json
from src.docs_templates import get_swagger_ui_html, get_redoc_html


# Create CFastAPI application
app = CFastAPI(
    title="One API",
    description="Multi-provider AI API proxy with management capabilities",
    version="2.0.0",
    debug=True  # Set to False in production
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"],
    allow_headers=["*"],
    allow_credentials=False
)


@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    print("CFastAPI One API starting up...")


@app.on_event("shutdown") 
async def shutdown_event():
    """Cleanup on shutdown"""
    print("CFastAPI One API shutting down...")


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "framework": "CFastAPI"
    }


# Documentation endpoints
@app.get("/api/docs")
async def get_docs():
    """Serve Swagger UI documentation"""
    html_content = get_swagger_ui_html()
    return Response(
        html_content,
        headers={"Content-Type": "text/html"}
    )


@app.get("/api/redocs")
async def get_redoc():
    """Serve ReDoc documentation"""
    html_content = get_redoc_html()
    return Response(
        html_content,
        headers={"Content-Type": "text/html"}
    )


@app.get("/api/openapi.json")
async def get_openapi():
    """Serve OpenAPI JSON specification"""
    openapi_json = get_openapi_json()
    return openapi_json


# Static file serving for admin panel
@app.get("/")
async def serve_admin_panel(request, app):
    """Serve admin panel from static assets"""
    from workers import Response
    
    env = app.state.get('env')
    if not env:
        return Response("Environment not available", status=500)
    
    # Get assets binding
    try:
        assets_binding = env.ASSETS if hasattr(env, 'ASSETS') else env.get('ASSETS')
    except Exception:
        assets_binding = env.get('ASSETS') if hasattr(env, 'get') else None
    
    if assets_binding:
        # Serve index.html from assets
        try:
            url = URL.new(request.url)
            url.pathname = "/index.html"
            assets_response = await assets_binding.fetch(url)
            
            # Convert the JsProxy response to a proper Python Response
            text_content = await assets_response.text()
            headers = {}
            # Try to get content type
            try:
                content_type = assets_response.headers.get('content-type')
                if content_type:
                    headers['Content-Type'] = content_type
                else:
                    headers['Content-Type'] = 'text/html'
            except:
                headers['Content-Type'] = 'text/html'
            
            return Response(
                text_content,
                status=int(assets_response.status) if hasattr(assets_response, 'status') else 200,
                headers=headers
            )
        except Exception as e:
            print(f"Error serving assets: {str(e)}")
            return Response(
                f'{{"error": "Failed to serve assets: {str(e)}"}}',
                status=500,
                headers={"Content-Type": "application/json"}
            )
    else:
        return Response(
            '{"message": "One API - CFastAPI Version"}',
            headers={"Content-Type": "application/json"}
        )


# Include routers
# Note: We need to manually add the routes since CFastAPI router structure is different
async def handle_v1_routes(request, app_instance):
    """Handle v1 API routes"""
    url = URL.new(request.url)
    path = url.pathname
    
    # Remove /v1 prefix for routing
    if path.startswith('/v1'):
        modified_request = request
        # We would need to modify the request path here
        # For now, handle manually
        
        # Route to appropriate handler
        if path.startswith('/v1/chat/completions'):
            from src.api.v1.chat import router as chat_router
            for route in chat_router.routes:
                if route.matches(request.method, '/completions'):
                    return await route.call(request, app_instance)
        
        elif path.startswith('/v1/completions'):
            from src.api.v1.completions import router as completions_router
            for route in completions_router.routes:
                if route.matches(request.method, '/completions'):
                    return await route.call(request, app_instance)
        
        elif path.startswith('/v1/models'):
            from src.api.v1.models import router as models_router
            # Handle models routes
            if path == '/v1/models':
                for route in models_router.routes:
                    if route.matches(request.method, ''):
                        return await route.call(request, app_instance)
            else:
                # Model detail route
                model_id = path.replace('/v1/models/', '')
                for route in models_router.routes:
                    path_params = route.matches(request.method, f'/{model_id}')
                    if path_params is not None:
                        return await route.call(request, app_instance, path_params)
    
    # No route found
    from src.cfastapi import HTTPException
    raise HTTPException(status_code=404, detail="Not Found")


async def handle_admin_routes(request, app_instance):
    """Handle admin API routes"""
    url = URL.new(request.url)
    path = url.pathname
    
    # Remove /api/admin prefix for routing
    if path.startswith('/api/admin'):
        admin_path = path.replace('/api/admin', '')
        
        # Route to appropriate admin handler
        from src.api.admin import admin_router
        for route in admin_router.routes:
            path_params = route.matches(request.method, admin_path)
            if path_params is not None:
                return await route.call(request, app_instance, path_params)
    
    # No route found
    from src.cfastapi import HTTPException
    raise HTTPException(status_code=404, detail="Not Found")


# Override the default handle method to add custom routing
original_handle = app.handle


async def custom_handle(request, env: Dict[str, Any], context=None) -> Response:
    """Custom request handler with routing"""
    try:
        # Store env in app state
        app.state['env'] = env
        app.state['context'] = context
        
        url = URL.new(request.url)
        path = url.pathname
        
        # Initialize database on first request
        if not app.state.get('_db_initialized', False):
            print("Initializing database...")
            try:
                db_binding = env.DB if hasattr(env, 'DB') else env.get('DB')
                if not db_binding:
                    print("Database binding not found")
                    return create_response({"error": "Database binding not available"}, 500)
                
                db = DatabaseManager(db_binding)
                success = await db.initialize()
                if success:
                    print("Database initialized successfully")
                    app.state['_db_initialized'] = True
                else:
                    print("Database initialization failed")
                    return create_response({"error": "Database initialization failed"}, 500)
            except Exception as e:
                print(f"Database initialization error: {str(e)}")
                import traceback
                print(f"Traceback: {traceback.format_exc()}")
                return create_response({"error": f"Database initialization failed: {str(e)}"}, 500)
        
        # Route requests
        if path.startswith('/v1/'):
            return await handle_v1_routes(request, app)
        elif path.startswith('/api/admin/'):
            return await handle_admin_routes(request, app)
        else:
            # Use default CFastAPI handling for other routes
            return await original_handle(request, env, context)
            
    except Exception as e:
        print(f"Error in request handler: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return create_response({
            "error": str(e),
            "type": type(e).__name__
        }, 500)


# Replace the handle method
app.handle = custom_handle


# Cloudflare Workers Python entry point
async def on_fetch(request, env, context=None):
    """Main entry point for Cloudflare Workers Python runtime"""
    return await app.handle(request, env, context)