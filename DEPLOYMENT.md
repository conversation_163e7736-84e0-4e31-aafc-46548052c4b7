# 部署指南 - AWSL One API Python

本文档提供了详细的部署步骤和配置说明。

## 📋 部署前准备

### 1. 环境要求
- Cloudflare Workers 账户
- Node.js 18+ 和 pnpm
- 已配置的自定义域名（可选但推荐）

### 2. 账户设置
确保你的 Cloudflare 账户已经：
- 启用了 Workers 服务
- 启用了 D1 数据库服务
- 配置了自定义域名（如果使用）

## 🚀 部署步骤

### Step 1: 克隆和安装依赖

```bash
# 进入项目目录
cd /path/to/awsl-one-api-python

# 安装依赖
pnpm install
```

### Step 2: 创建 D1 数据库

```bash
# 创建新的 D1 数据库
wrangler d1 create awsl-one-api-python
```

命令执行后会显示数据库信息，类似：
```
✅ Successfully created DB 'awsl-one-api-python'!

[[d1_databases]]
binding = "DB"
database_name = "awsl-one-api-python"
database_id = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
```

### Step 3: 配置 wrangler.toml

复制模板并编辑配置：

```bash
cp wrangler.toml.template wrangler.toml
```

编辑 `wrangler.toml` 文件：

```toml
name = "awsl-one-api-python"
main = "main.py"
compatibility_date = "2025-04-28"
compatibility_flags = ["python_workers"]

# 配置你的域名
routes = [
    { pattern = "your-api-domain.com", custom_domain = true },
]

[vars]
# 设置强密码作为管理员令牌
ADMIN_TOKEN = "admin-your-secure-token-here-12345"

[assets]
directory = "public"
binding = "ASSETS"
run_worker_first = true

# 使用第二步创建的数据库信息
[[d1_databases]]
binding = "DB"
database_name = "awsl-one-api-python"
database_id = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"

[python]
version = "3.12"
```

**重要配置项说明：**

- `name`: Workers 服务名称，需要全局唯一
- `routes`: 自定义域名配置，如果没有域名可以注释掉这行
- `ADMIN_TOKEN`: 管理员认证令牌，请设置复杂密码
- `database_id`: 使用第二步获得的数据库 ID

### Step 4: 部署到 Cloudflare

```bash
# 部署应用
pnpm run deploy
```

部署成功后会显示 Workers URL，类似：
```
✨ Successfully published your Worker to the following routes:
  • your-api-domain.com/*
  • awsl-one-api-python.your-subdomain.workers.dev/*
```

### Step 5: 初始化数据库

使用管理员令牌初始化数据库：

```bash
# 替换 URL 和令牌
curl -X POST https://your-api-domain.com/api/admin/db_initialize \
  -H "Authorization: Bearer admin-your-secure-token-here-12345"
```

成功响应：
```json
{"message": "Database initialized successfully"}
```

### Step 6: 验证部署

访问你的域名或 Workers URL，应该看到管理界面。

## 🔧 配置管理

### 通过 Web 界面配置

1. 访问 `https://your-api-domain.com`
2. 输入管理员令牌登录
3. 按照界面提示配置频道和令牌

### 通过 API 配置

#### 创建 OpenAI 频道：

```bash
curl -X POST https://your-api-domain.com/api/admin/channel \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin-your-secure-token-here-12345" \
  -d '{
    "name": "OpenAI 主频道",
    "type": "openai",
    "endpoint": "https://api.openai.com/v1/",
    "api_key": "sk-your-real-openai-api-key",
    "deployment_mapper": {
      "gpt-4": "gpt-4",
      "gpt-3.5-turbo": "gpt-3.5-turbo",
      "gpt-4-turbo": "gpt-4-turbo"
    }
  }'
```

#### 创建 Azure OpenAI 频道：

```bash
curl -X POST https://your-api-domain.com/api/admin/channel \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin-your-secure-token-here-12345" \
  -d '{
    "name": "Azure OpenAI 频道",
    "type": "azure-openai",
    "endpoint": "https://your-resource.openai.azure.com/",
    "api_key": "your-azure-openai-key",
    "api_version": "2024-02-15-preview",
    "deployment_mapper": {
      "gpt-4": "gpt-4-deployment",
      "gpt-35-turbo": "gpt-35-turbo-deployment"
    }
  }'
```

#### 创建 API 令牌：

```bash
curl -X POST https://your-api-domain.com/api/admin/token \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin-your-secure-token-here-12345" \
  -d '{
    "name": "用户令牌1",
    "channel_keys": [],
    "total_quota": 100.0
  }'
```

响应会包含生成的令牌：
```json
{
  "message": "Token created",
  "token": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
}
```

## 🧪 测试部署

### 测试 API 调用：

```bash
curl https://your-api-domain.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ],
    "max_tokens": 100
  }'
```

### 运行部署测试脚本：

```bash
# 先更新 deploy_test.py 中的配置
python deploy_test.py
```

## 🔍 故障排除

### 常见问题：

#### 1. 部署失败
- 检查 `wrangler.toml` 配置是否正确
- 确认 Cloudflare 账户权限
- 检查 Workers 服务是否启用

#### 2. 数据库连接失败
- 确认 D1 数据库 ID 正确
- 检查数据库是否在正确的账户下创建

#### 3. 管理界面无法访问
- 检查 `ASSETS` 绑定配置
- 确认 `public` 目录存在

#### 4. API 调用失败
- 确认频道配置正确
- 检查 API 密钥有效性
- 验证令牌配额未超限

### 查看日志：

```bash
# 查看实时日志
wrangler tail
```

### 本地开发调试：

```bash
# 本地运行
pnpm dev
```

## 📊 监控和维护

### 使用量监控

通过 Web 界面或 API 查看：
- 令牌使用情况
- 频道状态
- 费用统计

### 定期维护

1. **更新 API 密钥**：定期轮换 OpenAI/Azure API 密钥
2. **监控配额**：关注令牌使用情况，及时调整配额
3. **备份配置**：定期导出频道和令牌配置
4. **版本更新**：关注项目更新，及时升级

## 🔐 安全建议

1. **强密码**：使用复杂的管理员令牌
2. **HTTPS**：确保所有通信使用 HTTPS
3. **访问控制**：合理分配令牌权限和配额
4. **日志审计**：定期检查访问日志
5. **密钥轮换**：定期更换 API 密钥

## 📞 获取帮助

如果部署过程中遇到问题：

1. 检查本文档的故障排除部分
2. 查看 README.md 的详细说明
3. 检查 Cloudflare Workers 文档
4. 创建 GitHub Issue 寻求帮助

---

部署完成后，你就拥有了一个功能完整的 AI API 代理服务！🎉