name = "awsl-one-api-python"
main = "main.py"
compatibility_date = "2025-04-28"
compatibility_flags = ["python_workers"]

# Configure your custom domain here
routes = [
    { pattern = "your-domain.com-here", custom_domain = true },
]

[vars]
# Set your secure admin token for managing the API
ADMIN_TOKEN = "your-secure-admin-token-here"

[assets]
directory = "public"
binding = "ASSETS"
run_worker_first = true

# Cloudflare D1 Database configuration
[[d1_databases]]
binding = "DB"
# Create a D1 database and replace these values
database_name = "your-database-name-here"
database_id = "your-database-id-here"

# Python Workers configuration
[python]
# Python version - Cloudflare Workers supports Python 3.12
version = "3.12"