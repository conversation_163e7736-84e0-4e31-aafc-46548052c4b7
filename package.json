{"name": "awsl-one-api-python", "version": "1.0.0", "description": "AWSL One API - Python implementation for Cloudflare Workers", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy --minify", "cf-typegen": "wrangler types --include-env=false", "db:create": "wrangler d1 create awsl-one-api-python", "db:init": "curl -X POST https://your-domain.com/api/admin/db_initialize -H \"Authorization: Bearer your-admin-token\""}, "devDependencies": {"wrangler": "^4.20.5"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39", "dependencies": {"workers-mcp": "^0.0.13"}}