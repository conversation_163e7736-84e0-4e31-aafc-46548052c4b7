#!/usr/bin/env python3
"""
Quick diagnostic script for One API deployment
"""

import requests
import json
import sys

# Configuration
DOMAIN = "https://deepestcode.com"
ADMIN_TOKEN = "thisismytoken"

def check_endpoint(url, headers=None, method="GET", data=None):
    """Check an API endpoint"""
    try:
        if method == "GET":
            response = requests.get(url, headers=headers, timeout=10)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data, timeout=10)
        
        print(f"✅ {method} {url}")
        print(f"   Status: {response.status_code}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            try:
                json_data = response.json()
                print(f"   Response: {json.dumps(json_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"   Response: {response.text[:200]}...")
        else:
            print(f"   Response: {response.text[:200]}...")
        
        return response.status_code == 200, response
    
    except Exception as e:
        print(f"❌ {method} {url}")
        print(f"   Error: {str(e)}")
        return False, None

def main():
    print("🔍 One API 诊断工具")
    print("=" * 50)
    
    headers = {
        "Authorization": f"Bearer {ADMIN_TOKEN}",
        "Content-Type": "application/json"
    }
    
    print("\n1. 检查服务是否运行...")
    success, _ = check_endpoint(f"{DOMAIN}/")
    
    print("\n2. 检查健康状态...")
    success, _ = check_endpoint(f"{DOMAIN}/health")
    
    print("\n3. 检查管理员认证...")
    success, _ = check_endpoint(f"{DOMAIN}/api/admin/", headers=headers)
    
    print("\n4. 尝试初始化数据库...")
    success, _ = check_endpoint(f"{DOMAIN}/api/admin/db_initialize", headers=headers, method="POST")
    
    print("\n5. 检查现有token...")
    success, response = check_endpoint(f"{DOMAIN}/api/admin/token", headers=headers)
    
    if success and response:
        try:
            tokens = response.json()
            if isinstance(tokens, list) and len(tokens) == 0:
                print("\n6. 没有找到token，创建一个测试token...")
                test_token_data = {
                    "name": "测试Token",
                    "channel_keys": [],
                    "total_quota": 100.0,
                    "enabled": True
                }
                success, response = check_endpoint(
                    f"{DOMAIN}/api/admin/token", 
                    headers=headers, 
                    method="POST",
                    data=test_token_data
                )
                
                if success:
                    print("✅ 测试token创建成功！")
                    # 重新获取token列表
                    print("\n7. 重新获取token列表...")
                    check_endpoint(f"{DOMAIN}/api/admin/token", headers=headers)
            else:
                print(f"\n✅ 找到 {len(tokens)} 个token")
        except:
            pass
    
    print("\n8. 检查渠道配置...")
    check_endpoint(f"{DOMAIN}/api/admin/channel", headers=headers)
    
    print("\n" + "=" * 50)
    print("诊断完成！")
    print("\n💡 如果仍有问题，请检查：")
    print("1. wrangler.toml中的数据库配置是否正确")
    print("2. 是否已部署最新版本: wrangler deploy")
    print("3. Cloudflare Workers日志中是否有错误信息")

if __name__ == "__main__":
    main()