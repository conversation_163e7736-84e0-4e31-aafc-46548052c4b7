name = "one-api-py"
main = "main.py"
compatibility_date = "2025-04-28"
compatibility_flags = ["python_workers"]

# Replace with your domain
routes = [
    { pattern = "deepestcode.com", custom_domain = true },
]

[vars]
# Replace with your secure admin token
ADMIN_TOKEN = "thisismytoken"

[assets]
directory = "public"
binding = "ASSETS"
run_worker_first = true

# Cloudflare D1 Database
[[d1_databases]]
binding = "DB"
database_name = "db_oneapi"
database_id = "f2905836-66e8-4fa1-9118-3552b92123c6"

# Python Workers configuration
[python]
# Python version - currently supports 3.12
version = "3.12"

[observability.logs]
enabled = true