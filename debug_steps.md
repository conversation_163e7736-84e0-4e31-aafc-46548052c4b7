# 🔧 修复"加载令牌失败: HTTP 404"错误

## 问题分析
错误提示"HTTP 404: Not found"说明API token不存在于数据库中。这通常是因为：
1. 数据库未初始化
2. 没有创建任何API token
3. 使用了错误的token

## 解决步骤

### 步骤1: 更新配置使用增强版本
编辑 `wrangler.toml` 文件，将：
```toml
main = "main.py"
```
改为：
```toml
main = "main_enhanced.py"
```

### 步骤2: 确保数据库已初始化
访问管理界面或使用API初始化数据库：

**方法1 - 通过Web界面:**
1. 访问你的域名 (如: https://deepestcode.com)
2. 输入管理员token: `thisismytoken`
3. 切换到"📊 数据库"标签
4. 点击"🔄 初始化数据库"按钮

**方法2 - 通过API:**
```bash
curl -X POST https://deepestcode.com/api/admin/db_initialize \
  -H "Authorization: Bearer thisismytoken"
```

### 步骤3: 创建API Token
数据库初始化后，创建一个API token：

**方法1 - 通过Web界面:**
1. 切换到"🔑 Token管理"标签
2. 点击"➕ 添加Token"按钮
3. 填写Token信息:
   - 名称: 测试Token
   - 配额: 100.0
   - 渠道权限: 留空(访问所有渠道)
4. 点击"💾 保存Token"

**方法2 - 通过API:**
```bash
curl -X POST https://deepestcode.com/api/admin/token \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer thisismytoken" \
  -d '{
    "name": "测试Token",
    "channel_keys": [],
    "total_quota": 100.0,
    "enabled": true
  }'
```

### 步骤4: 获取Token列表
确认token已创建：
```bash
curl https://deepestcode.com/api/admin/token \
  -H "Authorization: Bearer thisismytoken"
```

### 步骤5: 创建渠道配置
如果还没有创建渠道，需要先创建一个：

**OpenAI渠道示例:**
```bash
curl -X POST https://deepestcode.com/api/admin/channel/openai-main \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer thisismytoken" \
  -d '{
    "name": "OpenAI主渠道",
    "type": "openai",
    "endpoint": "https://api.openai.com/v1/",
    "api_key": "你的OpenAI-API-Key",
    "deployment_mapper": {
      "gpt-4": "gpt-4",
      "gpt-3.5-turbo": "gpt-3.5-turbo"
    }
  }'
```

## 常见问题排查

### 问题1: 管理员认证失败
确保在请求头中正确设置了admin token:
```bash
# 正确的认证方式
-H "Authorization: Bearer thisismytoken"
# 或者
-H "x-admin-token: thisismytoken"
```

### 问题2: 数据库连接失败
检查 `wrangler.toml` 中的数据库配置是否正确:
```toml
[[d1_databases]]
binding = "DB"
database_name = "db_oneapi"  # 确保数据库存在
database_id = "你的数据库ID"   # 确保ID正确
```

### 问题3: 跨域问题
如果通过浏览器访问遇到CORS错误，确保请求包含正确的头部。

## 监控和调试

使用增强版本后，你可以访问新的监控端点：
- `/health` - 系统健康状态
- `/metrics` - 性能指标

查看详细日志信息来诊断问题。