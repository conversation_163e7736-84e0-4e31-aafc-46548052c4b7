"""
Constants and configuration values
"""

# API paths that should not serve static files
API_PATHS = [
    "/api",
    "/v1",
]

# Database version for migrations
DB_VERSION = "1.0.0"

# Settings keys
SETTINGS_KEYS = {
    "DB_VERSION": "db_version",
    "GLOBAL_PRICING": "global_pricing",
}

# Default pricing for models (per 1K tokens)
DEFAULT_PRICING = {
    "gpt-4": {"input": 0.03, "output": 0.06},
    "gpt-4-32k": {"input": 0.06, "output": 0.12},
    "gpt-3.5-turbo": {"input": 0.002, "output": 0.002},
    "gpt-3.5-turbo-16k": {"input": 0.004, "output": 0.004},
    "text-davinci-003": {"input": 0.02, "output": 0.02},
    "text-curie-001": {"input": 0.002, "output": 0.002},
    "text-babbage-001": {"input": 0.0005, "output": 0.0005},
    "text-ada-001": {"input": 0.0004, "output": 0.0004},
}

# Admin authentication header
ADMIN_TOKEN_HEADER = "Authorization"

# Provider types
PROVIDER_TYPES = {
    "OPENAI": "openai",
    "AZURE_OPENAI": "azure-openai",
}

# HTTP status codes
HTTP_STATUS = {
    "OK": 200,
    "CREATED": 201,
    "BAD_REQUEST": 400,
    "UNAUTHORIZED": 401,
    "FORBIDDEN": 403,
    "NOT_FOUND": 404,
    "INTERNAL_SERVER_ERROR": 500,
}

# Token prefix for generated API tokens
TOKEN_PREFIX = "sk-"

# Maximum quota value (in dollars)
MAX_QUOTA = 10000.0