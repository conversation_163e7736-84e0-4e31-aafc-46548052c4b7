"""
Constants used throughout the application
"""

from typing import List, Dict, Any

# Database version for migrations
DB_VERSION = "1.0.0"

# Valid settings keys  
SETTINGS_KEYS = {
    "DB_VERSION": "db_version",
    "GLOBAL_PRICING": "global_pricing",
    "MAX_TOKENS": "max_tokens",
    "DEFAULT_TEMPERATURE": "default_temperature",
    "RATE_LIMITS": "rate_limits",
    "MAINTENANCE_MODE": "maintenance_mode"
}

# Default pricing for models (per 1K tokens)
DEFAULT_PRICING: Dict[str, Dict[str, float]] = {
    "gpt-4": {
        "input": 0.03,
        "output": 0.06
    },
    "gpt-4-turbo": {
        "input": 0.01,
        "output": 0.03
    },
    "gpt-3.5-turbo": {
        "input": 0.0005,
        "output": 0.0015
    },
    "gpt-3.5-turbo-16k": {
        "input": 0.003,
        "output": 0.004
    },
    "claude-3-opus": {
        "input": 0.015,
        "output": 0.075
    },
    "claude-3-sonnet": {
        "input": 0.003,
        "output": 0.015
    },
    "claude-3-haiku": {
        "input": 0.00025,
        "output": 0.00125
    }
}

# API paths for routing
API_PATHS = [
    "/v1/",
    "/api/admin/",
    "/api/docs",
    "/api/redocs",
    "/api/openapi.json",
    "/health"
]

# Provider types
PROVIDER_TYPES = [
    "openai",
    "azure-openai",
    "anthropic",
    "google",
    "custom"
]

# HTTP status codes
HTTP_STATUS = {
    "OK": 200,
    "CREATED": 201,
    "BAD_REQUEST": 400,
    "UNAUTHORIZED": 401,
    "FORBIDDEN": 403,
    "NOT_FOUND": 404,
    "UNPROCESSABLE_ENTITY": 422,
    "TOO_MANY_REQUESTS": 429,
    "INTERNAL_SERVER_ERROR": 500
}

# Rate limiting defaults
RATE_LIMITS = {
    "requests_per_minute": 60,
    "requests_per_hour": 1000,
    "requests_per_day": 10000
}

# Token usage tracking
USAGE_TRACKING = {
    "enabled": True,
    "log_level": "INFO",
    "retention_days": 30
}

# Token configuration
TOKEN_PREFIX = "sk-"
ADMIN_TOKEN_HEADER = "authorization"

# Request limits
MAX_REQUEST_SIZE = 10 * 1024 * 1024  # 10MB
MAX_CONTEXT_LENGTH = 32768

# Timeout settings
DEFAULT_TIMEOUT = 30  # seconds
MAX_TIMEOUT = 120     # seconds