"""
Admin usage statistics API
"""

import logging
from datetime import datetime, timedelta
from typing import Optional

from src.cfastapi.routing import Router
from src.cfastapi import Depends, HTTPException
from src.schemas.admin import UsageStatsResponse
from src.schemas.common import SuccessResponse
from src.database import DatabaseManager

router = Router()
logger = logging.getLogger(__name__)


async def get_database(app) -> DatabaseManager:
    """Get database dependency"""
    env = app.state.get('env')
    if not env:
        raise HTTPException(status_code=500, detail="Environment not available")
    
    try:
        db_binding = env.DB if hasattr(env, 'DB') else env.get('DB')
    except Exception:
        db_binding = env.get('DB') if hasattr(env, 'get') else None
    
    return DatabaseManager(db_binding)


async def verify_admin_token(request, app) -> str:
    """Verify admin token"""
    env = app.state.get('env')
    if not env:
        raise HTTPException(status_code=500, detail="Environment not available")
    
    try:
        admin_token = env.ADMIN_TOKEN if hasattr(env, 'ADMIN_TOKEN') else env.get('ADMIN_TOKEN')
    except Exception:
        admin_token = env.get('ADMIN_TOKEN') if hasattr(env, 'get') else None
    
    if not admin_token:
        raise HTTPException(status_code=500, detail="Admin token not configured")
    
    auth_header = request.headers.get('authorization', '')
    if not auth_header.startswith('Bearer '):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    token = auth_header[7:]
    if token != admin_token:
        raise HTTPException(status_code=401, detail="Invalid admin token")
    
    return token


@router.get("/stats", response_model=UsageStatsResponse)
async def get_usage_statistics(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    token_key: Optional[str] = None,
    channel_key: Optional[str] = None,
    model: Optional[str] = None,
    admin_token: str = Depends(verify_admin_token),
    db: DatabaseManager = Depends(get_database)
):
    """Get usage statistics"""
    logger.info("Admin: Get usage statistics")
    
    # For now, return mock data since we don't have usage logging implemented yet
    # In a full implementation, this would query actual usage logs
    
    stats = {
        "total_requests": 0,
        "total_tokens": 0,
        "total_cost": 0.0,
        "model_breakdown": [],
        "start_date": start_date,
        "end_date": end_date
    }
    
    return UsageStatsResponse(**stats)