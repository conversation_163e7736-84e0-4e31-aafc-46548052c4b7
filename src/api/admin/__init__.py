"""
Admin API routes
"""

from src.cfastapi.routing import Router
from .channels import router as channels_router
from .tokens import router as tokens_router
from .settings import router as settings_router
from .usage import router as usage_router

# Create admin router
admin_router = Router(prefix="", tags=["admin"])

# Add routes from sub-routers with prefixes
for route in channels_router.routes:
    route.path = "/channels" + route.path
    route.path_pattern, route.path_params = route._compile_path(route.path)
    route.tags.append("admin-channels")

for route in tokens_router.routes:
    route.path = "/tokens" + route.path
    route.path_pattern, route.path_params = route._compile_path(route.path)
    route.tags.append("admin-tokens")

for route in settings_router.routes:
    route.path = "/settings" + route.path
    route.path_pattern, route.path_params = route._compile_path(route.path)
    route.tags.append("admin-settings")

for route in usage_router.routes:
    route.path = "/usage" + route.path
    route.path_pattern, route.path_params = route._compile_path(route.path)
    route.tags.append("admin-usage")

# Add all routes
admin_router.routes.extend([
    *channels_router.routes,
    *tokens_router.routes,
    *settings_router.routes,
    *usage_router.routes
])

__all__ = ["admin_router"]