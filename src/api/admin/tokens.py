"""
Admin tokens management API
"""

import logging
import secrets
import string

from src.cfastapi.routing import Router
from src.cfastapi import Depends, HTTPException
from src.schemas.admin import TokenCreateRequest, TokenUpdateRequest, TokenResponse
from src.schemas.common import SuccessResponse, ListResponse
from src.models import ApiTokenData
from src.database import DatabaseManager

router = Router()
logger = logging.getLogger(__name__)


async def get_database(app) -> DatabaseManager:
    """Get database dependency"""
    env = app.state.get('env')
    if not env:
        raise HTTPException(status_code=500, detail="Environment not available")
    
    try:
        db_binding = env.DB if hasattr(env, 'DB') else env.get('DB')
    except Exception:
        db_binding = env.get('DB') if hasattr(env, 'get') else None
    
    return DatabaseManager(db_binding)


async def verify_admin_token(request, app) -> str:
    """Verify admin token"""
    env = app.state.get('env')
    if not env:
        raise HTTPException(status_code=500, detail="Environment not available")
    
    try:
        admin_token = env.ADMIN_TOKEN if hasattr(env, 'ADMIN_TOKEN') else env.get('ADMIN_TOKEN')
    except Exception:
        admin_token = env.get('ADMIN_TOKEN') if hasattr(env, 'get') else None
    
    if not admin_token:
        raise HTTPException(status_code=500, detail="Admin token not configured")
    
    auth_header = request.headers.get('authorization', '')
    if not auth_header.startswith('Bearer '):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    token = auth_header[7:]
    if token != admin_token:
        raise HTTPException(status_code=401, detail="Invalid admin token")
    
    return token


def generate_token(length: int = 64) -> str:
    """Generate a secure random token"""
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))


@router.get("", response_model=ListResponse)
async def list_tokens(
    admin_token: str = Depends(verify_admin_token),
    db: DatabaseManager = Depends(get_database)
):
    """List all tokens"""
    logger.info("Admin: List tokens request")
    
    tokens = await db.get_all_tokens()
    token_data = []
    
    for key, token_obj in tokens.items():
        token_dict = token_obj.copy()
        token_dict['key'] = key
        token_data.append(token_dict)
    
    return ListResponse(
        data=token_data,
        total=len(token_data)
    )


@router.post("", response_model=TokenResponse)
async def create_token(
    request: TokenCreateRequest,
    admin_token: str = Depends(verify_admin_token),
    db: DatabaseManager = Depends(get_database)
):
    """Create a new token"""
    logger.info(f"Admin: Create token {request.name}")
    
    # Generate token key if not provided
    token_key = request.key or generate_token()
    
    # Check if token already exists
    existing = await db.get_api_token(token_key)
    if existing:
        raise HTTPException(
            status_code=400,
            detail="Token already exists"
        )
    
    # Create token data
    token_data = ApiTokenData.from_dict(request.dict())
    
    # Save to database
    success = await db.create_token(token_key, token_data)
    if not success:
        raise HTTPException(
            status_code=500,
            detail="Failed to create token"
        )
    
    logger.info(f"Admin: Token {request.name} created successfully")
    
    token_dict = token_data.copy()
    token_dict['key'] = token_key
    return TokenResponse(**token_dict)


@router.delete("/{token_key}", response_model=SuccessResponse)
async def delete_token(
    token_key: str,
    admin_token: str = Depends(verify_admin_token),
    db: DatabaseManager = Depends(get_database)
):
    """Delete a token"""
    logger.info(f"Admin: Delete token {token_key[:8]}...")
    
    existing = await db.get_api_token(token_key)
    if not existing:
        raise HTTPException(
            status_code=404,
            detail="Token not found"
        )
    
    success = await db.delete_token(token_key)
    if not success:
        raise HTTPException(
            status_code=500,
            detail="Failed to delete token"
        )
    
    logger.info(f"Admin: Token {token_key[:8]}... deleted successfully")
    return SuccessResponse(message="Token deleted successfully")