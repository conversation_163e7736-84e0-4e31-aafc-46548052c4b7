"""
Admin channels management API
"""

import logging
from typing import List

from src.cfastapi import CFastAPI, Depends, HTTPException
from src.cfastapi.routing import Router
from src.schemas.admin import (
    ChannelCreateRequest, ChannelUpdateRequest, ChannelResponse,
    SystemInfoResponse
)
from src.schemas.common import SuccessResponse, ListResponse
from src.models import ChannelConfig
from src.database import DatabaseManager

router = Router()
logger = logging.getLogger(__name__)


async def get_database(app) -> DatabaseManager:
    """Get database dependency"""
    env = app.state.get('env')
    if not env:
        raise HTTPException(status_code=500, detail="Environment not available")
    
    # Get DB binding from env
    try:
        db_binding = env.DB if hasattr(env, 'DB') else env.get('DB')
    except Exception:
        db_binding = env.get('DB') if hasattr(env, 'get') else None
    
    return DatabaseManager(db_binding)


async def verify_admin_token(request, app) -> str:
    """Verify admin token"""
    env = app.state.get('env')
    if not env:
        raise HTTPException(status_code=500, detail="Environment not available")
    
    # Get admin token from env
    try:
        admin_token = env.ADMIN_TOKEN if hasattr(env, 'ADMIN_TOKEN') else env.get('ADMIN_TOKEN')
    except Exception:
        admin_token = env.get('ADMIN_TOKEN') if hasattr(env, 'get') else None
    
    if not admin_token:
        raise HTTPException(status_code=500, detail="Admin token not configured")
    
    # Check authorization header
    auth_header = request.headers.get('authorization', '')
    if not auth_header.startswith('Bearer '):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    token = auth_header[7:]  # Remove 'Bearer ' prefix
    
    if token != admin_token:
        raise HTTPException(status_code=401, detail="Invalid admin token")
    
    return token


@router.get("", response_model=ListResponse)
async def list_channels(
    admin_token: str = Depends(verify_admin_token),
    db: DatabaseManager = Depends(get_database)
):
    """List all channels"""
    logger.info("Admin: List channels request")
    
    channels = await db.get_all_channels()
    channel_data = []
    
    for key, config in channels.items():
        channel_dict = config.copy()
        channel_dict['key'] = key
        channel_data.append(channel_dict)
    
    return ListResponse(
        data=channel_data,
        total=len(channel_data)
    )


@router.get("/{channel_key}", response_model=ChannelResponse)
async def get_channel(
    channel_key: str,
    admin_token: str = Depends(verify_admin_token),
    db: DatabaseManager = Depends(get_database)
):
    """Get a specific channel"""
    logger.info(f"Admin: Get channel {channel_key}")
    
    channel = await db.get_channel(channel_key)
    if not channel:
        raise HTTPException(
            status_code=404,
            detail=f"Channel '{channel_key}' not found"
        )
    
    channel_dict = channel.copy()
    channel_dict['key'] = channel_key
    return ChannelResponse(**channel_dict)


@router.post("", response_model=ChannelResponse)
async def create_channel(
    request: ChannelCreateRequest,
    admin_token: str = Depends(verify_admin_token),
    db: DatabaseManager = Depends(get_database)
):
    """Create a new channel"""
    logger.info(f"Admin: Create channel {request.key}")
    
    # Check if channel already exists
    existing = await db.get_channel(request.key)
    if existing:
        raise HTTPException(
            status_code=400,
            detail=f"Channel '{request.key}' already exists"
        )
    
    # Create channel config
    channel_config = ChannelConfig.from_dict(request.dict())
    
    # Save to database
    success = await db.create_channel(request.key, channel_config)
    if not success:
        raise HTTPException(
            status_code=500,
            detail="Failed to create channel"
        )
    
    logger.info(f"Admin: Channel {request.key} created successfully")
    
    channel_dict = channel_config.copy()
    channel_dict['key'] = request.key
    return ChannelResponse(**channel_dict)


@router.put("/{channel_key}", response_model=ChannelResponse)
async def update_channel(
    channel_key: str,
    request: ChannelUpdateRequest,
    admin_token: str = Depends(verify_admin_token),
    db: DatabaseManager = Depends(get_database)
):
    """Update an existing channel"""
    logger.info(f"Admin: Update channel {channel_key}")
    
    # Get existing channel
    existing_config = await db.get_channel(channel_key)
    if not existing_config:
        raise HTTPException(
            status_code=404,
            detail=f"Channel '{channel_key}' not found"
        )
    
    # Update with new values
    updated_config = existing_config.copy()
    update_data = request.dict(exclude_unset=True)
    updated_config.update(update_data)
    
    # Create new config object
    channel_config = ChannelConfig.from_dict(updated_config)
    
    # Save to database
    success = await db.update_channel(channel_key, channel_config)
    if not success:
        raise HTTPException(
            status_code=500,
            detail="Failed to update channel"
        )
    
    logger.info(f"Admin: Channel {channel_key} updated successfully")
    
    channel_dict = channel_config.copy()
    channel_dict['key'] = channel_key
    return ChannelResponse(**channel_dict)


@router.delete("/{channel_key}", response_model=SuccessResponse)
async def delete_channel(
    channel_key: str,
    admin_token: str = Depends(verify_admin_token),
    db: DatabaseManager = Depends(get_database)
):
    """Delete a channel"""
    logger.info(f"Admin: Delete channel {channel_key}")
    
    # Check if channel exists
    existing = await db.get_channel(channel_key)
    if not existing:
        raise HTTPException(
            status_code=404,
            detail=f"Channel '{channel_key}' not found"
        )
    
    # Delete channel
    success = await db.delete_channel(channel_key)
    if not success:
        raise HTTPException(
            status_code=500,
            detail="Failed to delete channel"
        )
    
    logger.info(f"Admin: Channel {channel_key} deleted successfully")
    return SuccessResponse(message=f"Channel '{channel_key}' deleted successfully")


@router.post("/{channel_key}/test", response_model=SuccessResponse)
async def test_channel(
    channel_key: str,
    admin_token: str = Depends(verify_admin_token),
    db: DatabaseManager = Depends(get_database)
):
    """Test a channel connection"""
    logger.info(f"Admin: Test channel {channel_key}")
    
    # Get channel
    channel = await db.get_channel(channel_key)
    if not channel:
        raise HTTPException(
            status_code=404,
            detail=f"Channel '{channel_key}' not found"
        )
    
    # For now, just return success if channel exists and is enabled
    if not channel.get('enabled', True):
        raise HTTPException(
            status_code=400,
            detail=f"Channel '{channel_key}' is disabled"
        )
    
    # TODO: Implement actual channel testing with provider APIs
    return SuccessResponse(message=f"Channel '{channel_key}' test passed")


@router.put("/{channel_key}/enable", response_model=SuccessResponse)
async def enable_channel(
    channel_key: str,
    admin_token: str = Depends(verify_admin_token),
    db: DatabaseManager = Depends(get_database)
):
    """Enable a channel"""
    logger.info(f"Admin: Enable channel {channel_key}")
    
    # Get existing channel
    existing_config = await db.get_channel(channel_key)
    if not existing_config:
        raise HTTPException(
            status_code=404,
            detail=f"Channel '{channel_key}' not found"
        )
    
    # Update enabled status
    updated_config = existing_config.copy()
    updated_config['enabled'] = True
    
    channel_config = ChannelConfig.from_dict(updated_config)
    success = await db.update_channel(channel_key, channel_config)
    
    if not success:
        raise HTTPException(
            status_code=500,
            detail="Failed to enable channel"
        )
    
    return SuccessResponse(message=f"Channel '{channel_key}' enabled")


@router.put("/{channel_key}/disable", response_model=SuccessResponse)
async def disable_channel(
    channel_key: str,
    admin_token: str = Depends(verify_admin_token),
    db: DatabaseManager = Depends(get_database)
):
    """Disable a channel"""
    logger.info(f"Admin: Disable channel {channel_key}")
    
    # Get existing channel
    existing_config = await db.get_channel(channel_key)
    if not existing_config:
        raise HTTPException(
            status_code=404,
            detail=f"Channel '{channel_key}' not found"
        )
    
    # Update enabled status
    updated_config = existing_config.copy()
    updated_config['enabled'] = False
    
    channel_config = ChannelConfig.from_dict(updated_config)
    success = await db.update_channel(channel_key, channel_config)
    
    if not success:
        raise HTTPException(
            status_code=500,
            detail="Failed to disable channel"
        )
    
    return SuccessResponse(message=f"Channel '{channel_key}' disabled")