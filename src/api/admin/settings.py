"""
Admin settings management API
"""

import logging
from typing import Dict, Any

from src.cfastapi.routing import Router
from src.cfastapi import Depends, HTTPException
from src.schemas.admin import SettingUpdateRequest, SettingResponse, GlobalPricingUpdateRequest
from src.schemas.common import SuccessResponse, ListResponse
from src.database import DatabaseManager

router = Router()
logger = logging.getLogger(__name__)


async def get_database(app) -> DatabaseManager:
    """Get database dependency"""
    env = app.state.get('env')
    if not env:
        raise HTTPException(status_code=500, detail="Environment not available")
    
    try:
        db_binding = env.DB if hasattr(env, 'DB') else env.get('DB')
    except Exception:
        db_binding = env.get('DB') if hasattr(env, 'get') else None
    
    return DatabaseManager(db_binding)


async def verify_admin_token(request, app) -> str:
    """Verify admin token"""
    env = app.state.get('env')
    if not env:
        raise HTTPException(status_code=500, detail="Environment not available")
    
    try:
        admin_token = env.ADMIN_TOKEN if hasattr(env, 'ADMIN_TOKEN') else env.get('ADMIN_TOKEN')
    except Exception:
        admin_token = env.get('ADMIN_TOKEN') if hasattr(env, 'get') else None
    
    if not admin_token:
        raise HTTPException(status_code=500, detail="Admin token not configured")
    
    auth_header = request.headers.get('authorization', '')
    if not auth_header.startswith('Bearer '):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    token = auth_header[7:]
    if token != admin_token:
        raise HTTPException(status_code=401, detail="Invalid admin token")
    
    return token


@router.get("/pricing/global")
async def get_global_pricing(
    admin_token: str = Depends(verify_admin_token),
    db: DatabaseManager = Depends(get_database)
) -> Dict[str, Dict[str, float]]:
    """Get global model pricing"""
    logger.info("Admin: Get global pricing")
    
    pricing = await db.get_setting('global_pricing', {})
    return pricing


@router.put("/pricing/global", response_model=SuccessResponse)
async def update_global_pricing(
    request: GlobalPricingUpdateRequest,
    admin_token: str = Depends(verify_admin_token),
    db: DatabaseManager = Depends(get_database)
):
    """Update global model pricing"""
    logger.info("Admin: Update global pricing")
    
    await db.set_setting('global_pricing', request.pricing)
    
    logger.info("Admin: Global pricing updated successfully")
    return SuccessResponse(message="Global pricing updated successfully")


@router.get("/system/info")
async def get_system_info(
    admin_token: str = Depends(verify_admin_token),
    db: DatabaseManager = Depends(get_database)
) -> Dict[str, Any]:
    """Get system information"""
    logger.info("Admin: Get system info")
    
    # Get counts
    channels = await db.get_all_channels()
    tokens = await db.get_all_tokens()
    
    # Get database version
    db_version = await db.get_setting('db_version', "unknown")
    
    system_info = {
        "version": "2.0.0",
        "database_version": db_version,
        "total_channels": len(channels),
        "enabled_channels": len([ch for ch in channels.values() if ch.get('enabled', True)]),
        "total_tokens": len(tokens),
        "enabled_tokens": len([tk for tk in tokens.values() if tk.get('enabled', True)]),
        "total_models": len(set().union(*[ch.get('deployment_mapper', {}).keys() for ch in channels.values()])),
    }
    
    return system_info