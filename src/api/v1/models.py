"""
Models API endpoints
"""

import time
import logging
from typing import List

from src.cfastapi import CFastAPI, Depends, HTTPException
from src.cfastapi.routing import Router
from src.schemas.models import ModelInfo, ModelsResponse
from src.models import ApiTokenData
from src.database import DatabaseManager

router = Router()
logger = logging.getLogger(__name__)


async def get_database(app) -> DatabaseManager:
    """Get database dependency"""
    env = app.state.get('env')
    if not env:
        raise HTTPException(status_code=500, detail="Environment not available")
    
    # Get DB binding from env
    try:
        db_binding = env.DB if hasattr(env, 'DB') else env.get('DB')
    except Exception:
        db_binding = env.get('DB') if hasattr(env, 'get') else None
    
    return DatabaseManager(db_binding)


async def get_current_token(request, db: DatabaseManager = Depends(get_database)) -> ApiTokenData:
    """Get and validate current API token"""
    auth_header = request.headers.get('authorization', '')
    if not auth_header.startswith('Bearer '):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    token = auth_header[7:]  # Remove 'Bearer ' prefix
    
    # Get token from database
    token_data = await db.get_api_token(token)
    if not token_data:
        raise HTTPException(status_code=401, detail="Invalid API token")
    
    # Check if token is enabled
    if not token_data.enabled:
        raise HTTPException(status_code=401, detail="Token is disabled")
    
    return token_data


@router.get("", response_model=ModelsResponse)
async def list_models(
    token: ApiTokenData = Depends(get_current_token),
    db: DatabaseManager = Depends(get_database)
):
    """
    List available models for the authenticated token
    """
    logger.info(f"List models request from token: {token.key[:8]}...")
    
    # Get available channels for this token
    channels = await _get_available_channels_for_token(db, token)
    
    # Collect all available models
    available_models = set()
    for channel in channels:
        deployment_mapper = channel.get('deployment_mapper', {})
        for model in deployment_mapper.keys():
            available_models.add(model)
    
    # Create model info objects
    models_data = []
    current_time = int(time.time())
    
    for model in sorted(available_models):
        model_info = ModelInfo(
            id=model,
            created=current_time,
            owned_by="openai"  # For compatibility
        )
        models_data.append(model_info)
    
    logger.info(f"Returning {len(models_data)} models for token {token.key[:8]}...")
    
    return ModelsResponse(data=models_data)


@router.get("/{model_id}", response_model=ModelInfo)
async def retrieve_model(
    model_id: str,
    token: ApiTokenData = Depends(get_current_token),
    db: DatabaseManager = Depends(get_database)
):
    """
    Retrieve information about a specific model
    """
    logger.info(f"Retrieve model {model_id} request from token: {token.key[:8]}...")
    
    # Get available channels for this token that support this model
    channels = await _get_available_channels_for_token(db, token, model_id)
    
    if not channels:
        raise HTTPException(
            status_code=404,
            detail=f"Model '{model_id}' not found"
        )
    
    # Return model info
    model_info = ModelInfo(
        id=model_id,
        created=int(time.time()),
        owned_by="openai"  # For compatibility
    )
    
    return model_info


async def _get_available_channels_for_token(db: DatabaseManager, token_data: ApiTokenData, model: str = None):
    """Get available channels for a token"""
    all_channels = await db.get_all_channels()
    
    # Get channels the token has access to
    if token_data.channel_keys:
        channels_list = []
        for key in token_data.channel_keys:
            if key in all_channels and all_channels[key].get('enabled', True):
                channel_data = all_channels[key].copy()
                channel_data['key'] = key
                channels_list.append(channel_data)
    else:
        # Token has access to all channels
        channels_list = []
        for key, config in all_channels.items():
            if config.get('enabled', True):
                channel_data = config.copy()
                channel_data['key'] = key
                channels_list.append(channel_data)
    
    # Filter by model if specified
    if model:
        channels_list = [
            ch for ch in channels_list 
            if model in ch.get('deployment_mapper', {})
        ]
    
    return channels_list