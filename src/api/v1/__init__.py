"""
v1 API routes
"""

from src.cfastapi.routing import Router
from .chat import router as chat_router
from .completions import router as completions_router
from .models import router as models_router

# Create v1 router
v1_router = Router(prefix="", tags=["v1"])

# Add routes from sub-routers
v1_router.routes.extend([
    # Chat routes
    *[route for route in chat_router.routes],
    # Completion routes  
    *[route for route in completions_router.routes],
    # Model routes
    *[route for route in models_router.routes]
])

__all__ = ["v1_router"]