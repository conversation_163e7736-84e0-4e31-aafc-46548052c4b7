"""
Text completions API endpoints
"""

import time
import uuid
import random
import logging
from typing import Dict, Any

from src.cfastapi import CFastAP<PERSON>, Depends, HTTPException
from src.cfastapi.routing import Router
from src.schemas.completion import CompletionRequest, CompletionResponse
from src.schemas.common import ErrorResponse
from src.models import ApiTokenData, ChannelConfig
from src.database import DatabaseManager
from src.providers import get_provider_handler

router = Router()
logger = logging.getLogger(__name__)


async def get_database(app) -> DatabaseManager:
    """Get database dependency"""
    env = app.state.get('env')
    if not env:
        raise HTTPException(status_code=500, detail="Environment not available")
    
    # Get DB binding from env
    try:
        db_binding = env.DB if hasattr(env, 'DB') else env.get('DB')
    except Exception:
        db_binding = env.get('DB') if hasattr(env, 'get') else None
    
    return DatabaseManager(db_binding)


async def get_current_token(request, db: DatabaseManager = Depends(get_database)) -> ApiTokenData:
    """Get and validate current API token"""
    auth_header = request.headers.get('authorization', '')
    if not auth_header.startswith('Bearer '):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    token = auth_header[7:]  # Remove 'Bearer ' prefix
    
    # Get token from database
    token_data = await db.get_api_token(token)
    if not token_data:
        raise HTTPException(status_code=401, detail="Invalid API token")
    
    # Check if token is enabled
    if not token_data.enabled:
        raise HTTPException(status_code=401, detail="Token is disabled")
    
    # Check quota
    if token_data.quota_exceeded:
        raise HTTPException(
            status_code=429,
            detail=f"Quota exceeded. Used: ${token_data.used_quota:.4f}, Limit: ${token_data.total_quota:.4f}"
        )
    
    return token_data


@router.post("/completions", response_model=CompletionResponse)
async def create_completion(
    request_data: CompletionRequest,
    request,
    app,
    token: ApiTokenData = Depends(get_current_token),
    db: DatabaseManager = Depends(get_database)
):
    """
    Create a text completion
    """
    start_time = time.time()
    request_id = str(uuid.uuid4())
    
    try:
        logger.info(f"Text completion request: {request_id} - Model: {request_data.model}")
        
        # Get available channels for this token and model
        channels = await _get_available_channels(db, token, request_data.model)
        if not channels:
            raise HTTPException(
                status_code=404,
                detail=f"No available channels for model: {request_data.model}"
            )
        
        # Select a random channel (simple load balancing)
        selected_channel = random.choice(channels)
        logger.info(f"Selected channel: {selected_channel['key']} for request {request_id}")
        
        # Check quota
        estimated_cost = await _estimate_cost(db, selected_channel, request_data)
        if not _can_afford(token, estimated_cost):
            raise HTTPException(
                status_code=429,
                detail="Insufficient quota"
            )
        
        # Get provider handler and make request
        provider_handler = get_provider_handler(selected_channel['type'])
        if not provider_handler:
            raise HTTPException(
                status_code=500,
                detail=f"Unsupported provider type: {selected_channel['type']}"
            )
        
        # Convert request data to dict for provider
        provider_request_data = request_data.dict()
        
        # Make provider request
        provider_response = await provider_handler.make_request(
            selected_channel, provider_request_data, request
        )
        
        # Handle response
        response_data = await _process_provider_response(
            provider_response, db, token.key, selected_channel, request_data.model, 
            request_id, start_time
        )
        
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in text completion {request_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Request failed: {str(e)}")


async def _get_available_channels(db: DatabaseManager, token_data: ApiTokenData, model: str):
    """Get channels available for token and model"""
    all_channels = await db.get_all_channels()
    
    # Filter by token permissions
    if token_data.channel_keys:
        allowed_channels = {key: config for key, config in all_channels.items() 
                         if key in token_data.channel_keys}
    else:
        allowed_channels = all_channels
    
    # Filter by model support
    available_channels = []
    for key, config in allowed_channels.items():
        if _channel_supports_model(config, model):
            channel_data = config.copy()
            channel_data['key'] = key
            available_channels.append(channel_data)
    
    return available_channels


def _channel_supports_model(channel_config: ChannelConfig, model: str) -> bool:
    """Check if channel supports the requested model"""
    deployment_mapper = channel_config.get('deployment_mapper', {})
    return model in deployment_mapper


async def _estimate_cost(db: DatabaseManager, channel: Dict[str, Any], request_data: CompletionRequest) -> float:
    """Estimate request cost"""
    # Simple estimation based on prompt
    if isinstance(request_data.prompt, str):
        estimated_tokens = len(request_data.prompt.split()) * 1.3
    elif isinstance(request_data.prompt, list) and request_data.prompt and isinstance(request_data.prompt[0], str):
        estimated_tokens = sum(len(p.split()) for p in request_data.prompt) * 1.3
    else:
        estimated_tokens = 100  # Default estimation for token-based prompts
    
    # Get pricing
    model_pricing = channel.get('model_pricing', {}).get(request_data.model)
    if not model_pricing:
        global_pricing = await db.get_setting('global_pricing', {})
        model_pricing = global_pricing.get(request_data.model, {"input": 0.001, "output": 0.002})
    
    return (estimated_tokens / 1000) * model_pricing.get("input", 0.001)


def _can_afford(token: ApiTokenData, estimated_cost: float) -> bool:
    """Check if token can afford estimated cost"""
    if token.total_quota <= 0:  # Unlimited quota
        return True
    return (token.used_quota + estimated_cost) <= token.total_quota


async def _process_provider_response(
    provider_response, 
    db: DatabaseManager, 
    token_key: str, 
    channel: Dict[str, Any], 
    model: str,
    request_id: str,
    start_time: float
) -> Dict[str, Any]:
    """Process provider response and log usage"""
    try:
        response_data = await provider_response.json()
        
        # Log usage if successful
        if provider_response.status == 200:
            await _log_usage(
                db, token_key, channel['key'], model, response_data,
                channel, request_id, time.time() - start_time
            )
        
        # Update response with request ID
        if isinstance(response_data, dict):
            response_data["id"] = request_id
        
        return response_data
        
    except Exception as e:
        logger.error(f"Error processing provider response: {str(e)}")
        raise HTTPException(status_code=500, detail="Error processing response")


async def _log_usage(
    db: DatabaseManager,
    token_key: str,
    channel_key: str,
    model: str,
    response_data: Dict[str, Any],
    channel: Dict[str, Any],
    request_id: str,
    response_time: float
):
    """Log usage for the request"""
    try:
        usage_data = response_data.get("usage", {})
        if not usage_data:
            logger.warning(f"No usage data in response for request {request_id}")
            return
        
        # Get pricing
        model_pricing = channel.get('model_pricing', {}).get(model)
        if not model_pricing:
            global_pricing = await db.get_setting('global_pricing', {})
            model_pricing = global_pricing.get(model, {"input": 0.001, "output": 0.002})
        
        # Calculate cost
        prompt_tokens = usage_data.get('prompt_tokens', 0)
        completion_tokens = usage_data.get('completion_tokens', 0)
        input_cost = (prompt_tokens / 1000) * model_pricing.get('input', 0)
        output_cost = (completion_tokens / 1000) * model_pricing.get('output', 0)
        total_cost = input_cost + output_cost
        
        # Update token usage
        await db.update_token_usage(token_key, total_cost)
        
        logger.info(
            f"Usage logged: {request_id} - "
            f"Tokens: {usage_data.get('total_tokens', 0)}, Cost: ${total_cost:.6f}"
        )
        
    except Exception as e:
        logger.error(f"Error logging usage for request {request_id}: {str(e)}")
        # Don't raise here - we don't want usage logging errors to fail the request