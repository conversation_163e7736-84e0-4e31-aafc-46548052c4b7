"""
Utility functions for the One API application
"""

import json
import secrets
import string
from typing import Dict, Any, Optional, Union
from workers import Response
from src.constants import TOKEN_PREFIX


def parse_json_safe(data: Union[str, bytes, Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    """Safely parse JSON data"""
    try:
        if isinstance(data, dict):
            return data
        if isinstance(data, (str, bytes)):
            return json.loads(data)
        return None
    except (json.JSONDecodeError, TypeError):
        return None


async def get_request_json(request) -> Optional[Dict[str, Any]]:
    """Extract JSON data from request"""
    try:
        text = await request.text()
        return parse_json_safe(text)
    except:
        return None


def create_response(data: Union[Dict[str, Any], str], status: int = 200, headers: Optional[Dict[str, str]] = None) -> Response:
    """Create a Response object with JSON data"""
    if headers is None:
        headers = {}
    
    # Set default headers
    default_headers = {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
    }
    default_headers.update(headers)
    
    # Convert data to JSON string if it's a dict or list
    if isinstance(data, (dict, list)):
        body = json.dumps(data, ensure_ascii=False)
    else:
        body = str(data)
    
    # Use Python Workers Response API
    return Response(body, status=status, headers=default_headers)


def create_html_response(html_content: str, status: int = 200, headers: Optional[Dict[str, str]] = None) -> Response:
    """Create a Response object with HTML content"""
    if headers is None:
        headers = {}
    
    # Set default headers for HTML
    default_headers = {
        "Content-Type": "text/html; charset=utf-8",
        "Access-Control-Allow-Origin": "*",
    }
    default_headers.update(headers)
    
    # Use Python Workers Response API
    return Response(html_content, status=status, headers=default_headers)


def create_cors_response() -> Response:
    """Create a CORS preflight response"""
    headers = {
        "Content-Type": "text/plain",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
        "Access-Control-Max-Age": "86400",
    }
    return Response("", status=200, headers=headers)


def generate_api_token(length: int = 32) -> str:
    """Generate a new API token"""
    alphabet = string.ascii_letters + string.digits
    token_body = ''.join(secrets.choice(alphabet) for _ in range(length))
    return f"{TOKEN_PREFIX}{token_body}"


def validate_channel_config(config: Dict[str, Any]) -> tuple[bool, str]:
    """Validate channel configuration"""
    required_fields = ["name", "type", "endpoint", "api_key"]
    
    for field in required_fields:
        if not config.get(field):
            return False, f"Missing required field: {field}"
    
    channel_type = config["type"]
    if channel_type not in ["openai", "azure-openai"]:
        return False, f"Invalid channel type: {channel_type}"
    
    if channel_type == "azure-openai" and not config.get("api_version"):
        return False, "Azure OpenAI channels require api_version"
    
    # Validate endpoint URL format
    endpoint = config["endpoint"]
    if not endpoint.startswith(("http://", "https://")):
        return False, "Endpoint must be a valid HTTP/HTTPS URL"
    
    return True, ""


def validate_token_config(config: Dict[str, Any]) -> tuple[bool, str]:
    """Validate token configuration"""
    if not config.get("name"):
        return False, "Token name is required"
    
    total_quota = config.get("total_quota", 0.0)
    if not isinstance(total_quota, (int, float)) or total_quota < 0:
        return False, "Total quota must be a non-negative number"
    
    channel_keys = config.get("channel_keys", [])
    if not isinstance(channel_keys, list):
        return False, "Channel keys must be a list"
    
    return True, ""


def format_currency(amount: float) -> str:
    """Format currency amount"""
    return f"${amount:.4f}"


def format_tokens(tokens: int) -> str:
    """Format token count with K/M suffixes"""
    if tokens >= 1_000_000:
        return f"{tokens / 1_000_000:.1f}M"
    elif tokens >= 1_000:
        return f"{tokens / 1_000:.1f}K"
    else:
        return str(tokens)


def truncate_string(text: str, max_length: int = 50) -> str:
    """Truncate string with ellipsis"""
    if len(text) <= max_length:
        return text
    return text[:max_length - 3] + "..."


def sanitize_channel_key(name: str) -> str:
    """Generate a safe channel key from name"""
    # Remove special characters and convert to lowercase
    import re
    key = re.sub(r'[^a-zA-Z0-9\-_]', '-', name.lower())
    # Remove multiple consecutive hyphens
    key = re.sub(r'-+', '-', key)
    # Remove leading/trailing hyphens
    return key.strip('-')


def build_azure_url(endpoint: str, deployment: str, api_version: str) -> str:
    """Build Azure OpenAI API URL"""
    # Ensure endpoint ends with /
    if not endpoint.endswith('/'):
        endpoint += '/'
    
    return f"{endpoint}openai/deployments/{deployment}/chat/completions?api-version={api_version}"


def extract_bearer_token(auth_header: str) -> Optional[str]:
    """Extract token from Authorization header"""
    if not auth_header or not auth_header.startswith('Bearer '):
        return None
    return auth_header[7:]  # Remove 'Bearer ' prefix


def is_stream_request(request_data: Dict[str, Any]) -> bool:
    """Check if request is for streaming response"""
    return request_data.get("stream", False) is True


def parse_sse_data(line: str) -> Optional[Dict[str, Any]]:
    """Parse Server-Sent Events data line"""
    if not line.startswith("data: "):
        return None
    
    data_str = line[6:]  # Remove "data: " prefix
    if data_str.strip() == "[DONE]":
        return {"done": True}
    
    return parse_json_safe(data_str)