"""
Admin API for managing channels and tokens
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from workers import Response
from src.database import DatabaseManager
from src.models import ChannelConfig, ApiTokenData
from src.constants import ADMIN_TOKEN_HEADER, HTTP_STATUS, SETTINGS_KEYS, DEFAULT_PRICING
from src.utils import (
    parse_json_safe,
    get_request_json,
    create_response,
    create_cors_response,
    generate_api_token,
    validate_channel_config,
    validate_token_config,
    sanitize_channel_key,
    extract_bearer_token,
)


class AdminAPI:
    """Admin API handler"""
    
    def __init__(self, db: DatabaseManager, admin_token: str):
        self.db = db
        self.admin_token = admin_token
    
    async def handle_request(self, request) -> Response:
        """Handle admin API requests"""
        # Handle CORS preflight
        if request.method == "OPTIONS":
            return create_cors_response()
        
        # Authenticate admin request
        if not await self._authenticate_admin(request):
            return create_response({"error": "Unauthorized"}, HTTP_STATUS["UNAUTHORIZED"])
        
        # Route to appropriate handler
        url_path = request.url.split('?')[0]  # Remove query parameters
        path_parts = url_path.split('/')[3:]  # Remove empty, 'api', 'admin'
        
        if not path_parts:
            return create_response({"message": "Admin API"}, HTTP_STATUS["OK"])
        
        endpoint = path_parts[0]
        
        if endpoint == "db_initialize":
            return await self._handle_db_initialize(request)
        elif endpoint == "channel":
            return await self._handle_channel_routes(request, path_parts[1:])
        elif endpoint == "token":
            return await self._handle_token_routes(request, path_parts[1:])
        elif endpoint == "pricing":
            return await self._handle_pricing_routes(request, path_parts[1:])
        else:
            return create_response({"error": "Not found"}, HTTP_STATUS["NOT_FOUND"])
    
    async def _authenticate_admin(self, request) -> bool:
        """Authenticate admin request"""
        # Try x-admin-token header first (for compatibility with frontend)
        admin_token = request.headers.get("x-admin-token", "")
        if admin_token and admin_token == self.admin_token:
            return True
        
        # Fallback to Authorization header
        auth_header = request.headers.get("Authorization", "")
        token = extract_bearer_token(auth_header)
        return token == self.admin_token
    
    # Database operations
    async def _handle_db_initialize(self, request) -> Response:
        """Initialize database"""
        if request.method != "POST":
            return create_response({"error": "Method not allowed"}, 405)
        
        success = await self.db.initialize()
        if success:
            return create_response({"message": "Database initialized successfully"}, HTTP_STATUS["OK"])
        else:
            return create_response({"error": "Database initialization failed"}, HTTP_STATUS["INTERNAL_SERVER_ERROR"])
    
    # Channel management
    async def _handle_channel_routes(self, request, path_parts) -> Response:
        """Handle channel management routes"""
        if request.method == "GET":
            if not path_parts:
                # GET /api/admin/channel - List all channels
                return await self._list_channels()
            else:
                # GET /api/admin/channel/{key} - Get specific channel
                return await self._get_channel(path_parts[0])
        
        elif request.method == "POST":
            if path_parts:
                # POST /api/admin/channel/{key} - Create or update channel
                return await self._create_or_update_channel(request, path_parts[0])
            else:
                # POST /api/admin/channel - Create new channel (legacy support)
                return await self._create_channel(request)
        
        elif request.method == "PUT":
            if path_parts:
                # PUT /api/admin/channel/{key} - Update channel (legacy support)
                return await self._update_channel(request, path_parts[0])
        
        elif request.method == "DELETE":
            if path_parts:
                # DELETE /api/admin/channel/{key} - Delete channel
                return await self._delete_channel(path_parts[0])
        
        return create_response({"error": "Method not allowed"}, 405)
    
    async def _list_channels(self) -> Response:
        """List all channels"""
        channels = await self.db.get_all_channels()
        channel_list = []
        
        for key, config in channels.items():
            # Convert config to dict and hide API key
            config_dict = config.to_dict()
            config_dict["api_key"] = "***"
            
            # Format as expected by frontend
            channel_list.append({
                "key": key,
                "value": json.dumps(config_dict)
            })
        
        # Return array directly (frontend expects array, not object with channels property)
        return create_response(channel_list, HTTP_STATUS["OK"])
    
    async def _get_channel(self, key: str) -> Response:
        """Get specific channel"""
        channel = await self.db.get_channel(key)
        if not channel:
            return create_response({"error": "Channel not found"}, HTTP_STATUS["NOT_FOUND"])
        
        channel_data = channel.to_dict()
        channel_data["key"] = key
        # Don't expose full API key
        channel_data["api_key"] = "***"
        
        return create_response({"channel": channel_data}, HTTP_STATUS["OK"])
    
    async def _create_channel(self, request) -> Response:
        """Create new channel"""
        data = await get_request_json(request)
        if not data:
            return create_response({"error": "Invalid JSON"}, HTTP_STATUS["BAD_REQUEST"])
        
        # Validate configuration
        valid, error_msg = validate_channel_config(data)
        if not valid:
            return create_response({"error": error_msg}, HTTP_STATUS["BAD_REQUEST"])
        
        # Generate channel key if not provided
        key = data.get("key") or sanitize_channel_key(data["name"])
        
        # Check if channel already exists
        if await self.db.channel_exists(key):
            return create_response({"error": "Channel already exists"}, HTTP_STATUS["BAD_REQUEST"])
        
        # Create channel config
        config = ChannelConfig.from_dict(data)
        
        # Save to database
        success = await self.db.create_channel(key, config)
        if success:
            return create_response({"message": "Channel created", "key": key}, HTTP_STATUS["CREATED"])
        else:
            return create_response({"error": "Failed to create channel"}, HTTP_STATUS["INTERNAL_SERVER_ERROR"])
    
    async def _update_channel(self, request, key: str) -> Response:
        """Update existing channel"""
        # Check if channel exists
        if not await self.db.channel_exists(key):
            return create_response({"error": "Channel not found"}, HTTP_STATUS["NOT_FOUND"])
        
        data = await get_request_json(request)
        if not data:
            return create_response({"error": "Invalid JSON"}, HTTP_STATUS["BAD_REQUEST"])
        
        # Validate configuration
        valid, error_msg = validate_channel_config(data)
        if not valid:
            return create_response({"error": error_msg}, HTTP_STATUS["BAD_REQUEST"])
        
        # Create channel config
        config = ChannelConfig.from_dict(data)
        
        # Update in database
        success = await self.db.update_channel(key, config)
        if success:
            return create_response({"message": "Channel updated"}, HTTP_STATUS["OK"])
        else:
            return create_response({"error": "Failed to update channel"}, HTTP_STATUS["INTERNAL_SERVER_ERROR"])
    
    async def _create_or_update_channel(self, request, key: str) -> Response:
        """Create or update channel (frontend uses POST for both)"""
        data = await get_request_json(request)
        if not data:
            return create_response({"error": "Invalid JSON"}, HTTP_STATUS["BAD_REQUEST"])
        
        # Validate configuration
        valid, error_msg = validate_channel_config(data)
        if not valid:
            return create_response({"error": error_msg}, HTTP_STATUS["BAD_REQUEST"])
        
        # Create channel config
        config = ChannelConfig.from_dict(data)
        
        # Check if channel exists
        channel_exists = await self.db.channel_exists(key)
        
        if channel_exists:
            # Update existing channel
            success = await self.db.update_channel(key, config)
            if success:
                return create_response({"message": "Channel updated"}, HTTP_STATUS["OK"])
            else:
                return create_response({"error": "Failed to update channel"}, HTTP_STATUS["INTERNAL_SERVER_ERROR"])
        else:
            # Create new channel
            success = await self.db.create_channel(key, config)
            if success:
                return create_response({"message": "Channel created", "key": key}, HTTP_STATUS["CREATED"])
            else:
                return create_response({"error": "Failed to create channel"}, HTTP_STATUS["INTERNAL_SERVER_ERROR"])
    
    async def _delete_channel(self, key: str) -> Response:
        """Delete channel"""
        # Check if channel exists
        if not await self.db.channel_exists(key):
            return create_response({"error": "Channel not found"}, HTTP_STATUS["NOT_FOUND"])
        
        # Delete from database
        success = await self.db.delete_channel(key)
        if success:
            return create_response({"message": "Channel deleted"}, HTTP_STATUS["OK"])
        else:
            return create_response({"error": "Failed to delete channel"}, HTTP_STATUS["INTERNAL_SERVER_ERROR"])
    
    # Token management
    async def _handle_token_routes(self, request, path_parts) -> Response:
        """Handle token management routes"""
        if request.method == "GET":
            if not path_parts:
                # GET /api/admin/token - List all tokens
                return await self._list_tokens()
            else:
                # GET /api/admin/token/{token} - Get specific token
                return await self._get_token(path_parts[0])
        
        elif request.method == "POST":
            if path_parts:
                # POST /api/admin/token/{token} - Create or update token
                return await self._create_or_update_token(request, path_parts[0])
            else:
                # POST /api/admin/token - Create new token (legacy support)
                return await self._create_token(request)
        
        elif request.method == "PUT":
            if path_parts:
                # PUT /api/admin/token/{token} - Update token (legacy support)
                return await self._update_token(request, path_parts[0])
        
        elif request.method == "DELETE":
            if path_parts:
                # DELETE /api/admin/token/{token} - Delete token
                return await self._delete_token(path_parts[0])
        
        return create_response({"error": "Method not allowed"}, 405)
    
    async def _list_tokens(self) -> Response:
        """List all tokens"""
        tokens = await self.db.get_all_tokens()
        token_list = []
        
        for token, config in tokens.items():
            # Convert config to dict
            config_dict = config.to_dict()
            
            # Format as expected by frontend
            token_list.append({
                "key": token,
                "value": json.dumps(config_dict),
                "usage": config.used_quota  # Include usage for display
            })
        
        # Return array directly (frontend expects array, not object with tokens property)
        return create_response(token_list, HTTP_STATUS["OK"])
    
    async def _get_token(self, token: str) -> Response:
        """Get specific token"""
        token_data = await self.db.get_api_token(token)
        if not token_data:
            return create_response({"error": "Token not found"}, HTTP_STATUS["NOT_FOUND"])
        
        data = token_data.to_dict()
        data["token"] = token
        
        return create_response({"token": data}, HTTP_STATUS["OK"])
    
    async def _create_token(self, request) -> Response:
        """Create new token"""
        data = await get_request_json(request)
        if not data:
            return create_response({"error": "Invalid JSON"}, HTTP_STATUS["BAD_REQUEST"])
        
        # Validate configuration
        valid, error_msg = validate_token_config(data)
        if not valid:
            return create_response({"error": error_msg}, HTTP_STATUS["BAD_REQUEST"])
        
        # Validate channel keys exist
        channel_keys = data.get("channel_keys", [])
        if channel_keys:
            for key in channel_keys:
                if not await self.db.channel_exists(key):
                    return create_response({"error": f"Channel not found: {key}"}, HTTP_STATUS["BAD_REQUEST"])
        
        # Generate new token
        token = generate_api_token()
        
        # Create token config
        config = ApiTokenData.from_dict(data)
        config.created_at = datetime.utcnow().isoformat()
        
        # Save to database
        success = await self.db.create_token(token, config)
        if success:
            return create_response({"message": "Token created", "token": token}, HTTP_STATUS["CREATED"])
        else:
            return create_response({"error": "Failed to create token"}, HTTP_STATUS["INTERNAL_SERVER_ERROR"])
    
    async def _update_token(self, request, token: str) -> Response:
        """Update existing token"""
        # Check if token exists
        if not await self.db.token_exists(token):
            return create_response({"error": "Token not found"}, HTTP_STATUS["NOT_FOUND"])
        
        data = await get_request_json(request)
        if not data:
            return create_response({"error": "Invalid JSON"}, HTTP_STATUS["BAD_REQUEST"])
        
        # Validate configuration
        valid, error_msg = validate_token_config(data)
        if not valid:
            return create_response({"error": error_msg}, HTTP_STATUS["BAD_REQUEST"])
        
        # Validate channel keys exist
        channel_keys = data.get("channel_keys", [])
        if channel_keys:
            for key in channel_keys:
                if not await self.db.channel_exists(key):
                    return create_response({"error": f"Channel not found: {key}"}, HTTP_STATUS["BAD_REQUEST"])
        
        # Get existing token data to preserve some fields
        existing_token = await self.db.get_api_token(token)
        if existing_token:
            # Preserve created_at and used_quota
            data["created_at"] = existing_token.created_at
            data["used_quota"] = existing_token.used_quota
        
        # Create token config
        config = ApiTokenData.from_dict(data)
        
        # Update in database
        success = await self.db.update_token(token, config)
        if success:
            return create_response({"message": "Token updated"}, HTTP_STATUS["OK"])
        else:
            return create_response({"error": "Failed to update token"}, HTTP_STATUS["INTERNAL_SERVER_ERROR"])
    
    async def _create_or_update_token(self, request, token: str) -> Response:
        """Create or update token (frontend uses POST for both)"""
        data = await get_request_json(request)
        if not data:
            return create_response({"error": "Invalid JSON"}, HTTP_STATUS["BAD_REQUEST"])
        
        # Validate configuration
        valid, error_msg = validate_token_config(data)
        if not valid:
            return create_response({"error": error_msg}, HTTP_STATUS["BAD_REQUEST"])
        
        # Validate channel keys exist
        channel_keys = data.get("channel_keys", [])
        if channel_keys:
            for key in channel_keys:
                if not await self.db.channel_exists(key):
                    return create_response({"error": f"Channel not found: {key}"}, HTTP_STATUS["BAD_REQUEST"])
        
        # Check if token exists
        token_exists = await self.db.token_exists(token)
        
        if token_exists:
            # Update existing token
            existing_token = await self.db.get_api_token(token)
            if existing_token:
                # Preserve created_at and used_quota
                data["created_at"] = existing_token.created_at
                data["used_quota"] = existing_token.used_quota
            
            config = ApiTokenData.from_dict(data)
            success = await self.db.update_token(token, config)
            if success:
                return create_response({"message": "Token updated"}, HTTP_STATUS["OK"])
            else:
                return create_response({"error": "Failed to update token"}, HTTP_STATUS["INTERNAL_SERVER_ERROR"])
        else:
            # Create new token
            config = ApiTokenData.from_dict(data)
            config.created_at = datetime.utcnow().isoformat()
            
            success = await self.db.create_token(token, config)
            if success:
                return create_response({"message": "Token created", "token": token}, HTTP_STATUS["CREATED"])
            else:
                return create_response({"error": "Failed to create token"}, HTTP_STATUS["INTERNAL_SERVER_ERROR"])
    
    async def _delete_token(self, token: str) -> Response:
        """Delete token"""
        # Check if token exists
        if not await self.db.token_exists(token):
            return create_response({"error": "Token not found"}, HTTP_STATUS["NOT_FOUND"])
        
        # Delete from database
        success = await self.db.delete_token(token)
        if success:
            return create_response({"message": "Token deleted"}, HTTP_STATUS["OK"])
        else:
            return create_response({"error": "Failed to delete token"}, HTTP_STATUS["INTERNAL_SERVER_ERROR"])
    
    # Pricing management
    async def _handle_pricing_routes(self, request, path_parts) -> Response:
        """Handle pricing management routes"""
        if request.method == "GET":
            # GET /api/admin/pricing - Get global pricing
            return await self._get_pricing()
        
        elif request.method == "POST":
            # POST /api/admin/pricing - Update global pricing
            return await self._update_pricing(request)
        
        return create_response({"error": "Method not allowed"}, 405)
    
    async def _get_pricing(self) -> Response:
        """Get global pricing configuration"""
        pricing = await self.db.get_setting(SETTINGS_KEYS["GLOBAL_PRICING"], DEFAULT_PRICING)
        # Return pricing data directly (frontend expects the pricing object, not wrapped)
        return create_response(pricing, HTTP_STATUS["OK"])
    
    async def _update_pricing(self, request) -> Response:
        """Update global pricing configuration"""
        data = await get_request_json(request)
        if not data:
            return create_response({"error": "Invalid JSON"}, HTTP_STATUS["BAD_REQUEST"])
        
        # Frontend sends pricing data directly, not wrapped in {"pricing": {...}}
        pricing = data
        if not isinstance(pricing, dict):
            return create_response({"error": "Pricing must be an object"}, HTTP_STATUS["BAD_REQUEST"])
        
        # Validate pricing structure
        for model, price_data in pricing.items():
            if not isinstance(price_data, dict):
                return create_response({"error": f"Invalid pricing for model {model}"}, HTTP_STATUS["BAD_REQUEST"])
            
            if "input" not in price_data or "output" not in price_data:
                return create_response({"error": f"Missing input/output pricing for model {model}"}, HTTP_STATUS["BAD_REQUEST"])
            
            try:
                float(price_data["input"])
                float(price_data["output"])
            except ValueError:
                return create_response({"error": f"Invalid pricing values for model {model}"}, HTTP_STATUS["BAD_REQUEST"])
        
        # Save pricing
        await self.db.set_setting(SETTINGS_KEYS["GLOBAL_PRICING"], pricing)
        
        return create_response({"message": "Pricing updated"}, HTTP_STATUS["OK"])