"""
Models API dataclass models
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Literal


@dataclass
class ModelPermission:
    """Model permission information"""
    id: str
    created: int
    object: str = "model_permission"
    allow_create_engine: bool = False
    allow_sampling: bool = True
    allow_logprobs: bool = True
    allow_search_indices: bool = False
    allow_view: bool = True
    allow_fine_tuning: bool = False
    organization: str = "*"
    group: Optional[str] = None
    is_blocking: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "object": self.object,
            "created": self.created,
            "allow_create_engine": self.allow_create_engine,
            "allow_sampling": self.allow_sampling,
            "allow_logprobs": self.allow_logprobs,
            "allow_search_indices": self.allow_search_indices,
            "allow_view": self.allow_view,
            "allow_fine_tuning": self.allow_fine_tuning,
            "organization": self.organization,
            "group": self.group,
            "is_blocking": self.is_blocking
        }


@dataclass
class ModelInfo:
    """Model information"""
    id: str
    created: int
    owned_by: str
    object: Literal["model"] = "model"
    permission: Optional[List[ModelPermission]] = None
    root: Optional[str] = None
    parent: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "id": self.id,
            "object": self.object,
            "created": self.created,
            "owned_by": self.owned_by,
            "root": self.root,
            "parent": self.parent
        }
        if self.permission is not None:
            result["permission"] = [p.to_dict() for p in self.permission]
        return result


@dataclass
class ModelsResponse:
    """Models list response"""
    data: List[ModelInfo]
    object: Literal["list"] = "list"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "object": self.object,
            "data": [model.to_dict() for model in self.data]
        }


@dataclass
class ModelCapabilities:
    """Model capabilities information"""
    completion: bool = True
    chat_completion: bool = True
    function_calling: bool = False
    streaming: bool = True
    max_tokens: Optional[int] = None
    context_length: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "completion": self.completion,
            "chat_completion": self.chat_completion,
            "function_calling": self.function_calling,
            "streaming": self.streaming,
            "max_tokens": self.max_tokens,
            "context_length": self.context_length
        }


@dataclass
class ModelPricing:
    """Model pricing information"""
    input: float
    output: float
    currency: str = "USD"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "input": self.input,
            "output": self.output,
            "currency": self.currency
        }


@dataclass
class DetailedModelInfo(ModelInfo):
    """Detailed model information with capabilities and pricing"""
    capabilities: Optional[ModelCapabilities] = None
    pricing: Optional[ModelPricing] = None
    description: Optional[str] = None
    max_tokens: Optional[int] = None
    context_length: Optional[int] = None
    provider: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = super().to_dict()
        if self.capabilities is not None:
            result["capabilities"] = self.capabilities.to_dict()
        if self.pricing is not None:
            result["pricing"] = self.pricing.to_dict()
        if self.description is not None:
            result["description"] = self.description
        if self.max_tokens is not None:
            result["max_tokens"] = self.max_tokens
        if self.context_length is not None:
            result["context_length"] = self.context_length
        if self.provider is not None:
            result["provider"] = self.provider
        return result