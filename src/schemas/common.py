"""
Common models and utilities
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List, Union
from enum import Enum


@dataclass
class Usage:
    """Token usage information"""
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "prompt_tokens": self.prompt_tokens,
            "completion_tokens": self.completion_tokens,
            "total_tokens": self.total_tokens
        }


@dataclass
class ErrorResponse:
    """Error response model"""
    error: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        return {"error": self.error}


@dataclass
class SuccessResponse:
    """Generic success response"""
    success: bool = True
    message: str = ""
    data: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "success": self.success,
            "message": self.message
        }
        if self.data is not None:
            result["data"] = self.data
        return result


@dataclass
class ListResponse:
    """Generic list response"""
    object: str = "list"
    data: List[Any] = field(default_factory=list)
    total: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "object": self.object,
            "data": self.data
        }
        if self.total is not None:
            result["total"] = self.total
        return result


class MessageRole(str, Enum):
    """Message role enumeration"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    FUNCTION = "function"


def validate_chat_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """Simple validation for chat completion requests"""
    errors = []
    
    # Required fields
    if "model" not in data:
        errors.append("Field 'model' is required")
    if "messages" not in data:
        errors.append("Field 'messages' is required")
    elif not isinstance(data["messages"], list) or len(data["messages"]) == 0:
        errors.append("Field 'messages' must be a non-empty list")
    
    # Validate messages
    if "messages" in data and isinstance(data["messages"], list):
        for i, msg in enumerate(data["messages"]):
            if not isinstance(msg, dict):
                errors.append(f"Message {i} must be an object")
                continue
            if "role" not in msg:
                errors.append(f"Message {i} missing required field 'role'")
            elif msg["role"] not in ["system", "user", "assistant", "function"]:
                errors.append(f"Message {i} has invalid role: {msg['role']}")
    
    # Optional field validation
    if "temperature" in data:
        temp = data["temperature"]
        if not isinstance(temp, (int, float)) or temp < 0 or temp > 2:
            errors.append("Field 'temperature' must be between 0 and 2")
    
    if "max_tokens" in data:
        max_tokens = data["max_tokens"]
        if not isinstance(max_tokens, int) or max_tokens < 1:
            errors.append("Field 'max_tokens' must be a positive integer")
    
    if errors:
        raise ValueError("; ".join(errors))
    
    return data