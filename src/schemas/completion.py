"""
Text completion dataclass models
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Union, Literal
from .common import Usage


@dataclass
class CompletionRequest:
    """Text completion request model"""
    model: str
    prompt: Union[str, List[str], List[int], List[List[int]]]
    suffix: Optional[str] = None
    max_tokens: Optional[int] = 16
    temperature: Optional[float] = 1.0
    top_p: Optional[float] = 1.0
    n: Optional[int] = 1
    stream: Optional[bool] = False
    logprobs: Optional[int] = None
    echo: Optional[bool] = False
    stop: Optional[Union[str, List[str]]] = None
    presence_penalty: Optional[float] = 0.0
    frequency_penalty: Optional[float] = 0.0
    best_of: Optional[int] = 1
    logit_bias: Optional[Dict[str, float]] = None
    user: Optional[str] = None
    
    def __post_init__(self):
        """Validate fields after initialization"""
        if isinstance(self.stop, list) and len(self.stop) > 4:
            raise ValueError('Stop sequences list cannot have more than 4 items')
        
        if self.best_of and self.n and self.best_of < self.n:
            raise ValueError('best_of must be greater than or equal to n')
            
        # Validate ranges
        if self.max_tokens is not None and self.max_tokens < 1:
            raise ValueError('max_tokens must be at least 1')
        if self.temperature is not None and (self.temperature < 0.0 or self.temperature > 2.0):
            raise ValueError('temperature must be between 0.0 and 2.0')
        if self.top_p is not None and (self.top_p < 0.0 or self.top_p > 1.0):
            raise ValueError('top_p must be between 0.0 and 1.0')
        if self.n is not None and (self.n < 1 or self.n > 10):
            raise ValueError('n must be between 1 and 10')
        if self.logprobs is not None and (self.logprobs < 0 or self.logprobs > 5):
            raise ValueError('logprobs must be between 0 and 5')
        if self.presence_penalty is not None and (self.presence_penalty < -2.0 or self.presence_penalty > 2.0):
            raise ValueError('presence_penalty must be between -2.0 and 2.0')
        if self.frequency_penalty is not None and (self.frequency_penalty < -2.0 or self.frequency_penalty > 2.0):
            raise ValueError('frequency_penalty must be between -2.0 and 2.0')
        if self.best_of is not None and (self.best_of < 1 or self.best_of > 20):
            raise ValueError('best_of must be between 1 and 20')
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "model": self.model,
            "prompt": self.prompt
        }
        
        # Add optional fields if they have non-default values
        if self.suffix is not None:
            result["suffix"] = self.suffix
        if self.max_tokens != 16:
            result["max_tokens"] = self.max_tokens
        if self.temperature != 1.0:
            result["temperature"] = self.temperature
        if self.top_p != 1.0:
            result["top_p"] = self.top_p
        if self.n != 1:
            result["n"] = self.n
        if self.stream:
            result["stream"] = self.stream
        if self.logprobs is not None:
            result["logprobs"] = self.logprobs
        if self.echo:
            result["echo"] = self.echo
        if self.stop is not None:
            result["stop"] = self.stop
        if self.presence_penalty != 0.0:
            result["presence_penalty"] = self.presence_penalty
        if self.frequency_penalty != 0.0:
            result["frequency_penalty"] = self.frequency_penalty
        if self.best_of != 1:
            result["best_of"] = self.best_of
        if self.logit_bias is not None:
            result["logit_bias"] = self.logit_bias
        if self.user is not None:
            result["user"] = self.user
            
        return result


@dataclass
class CompletionChoice:
    """Text completion choice"""
    text: str
    index: int
    logprobs: Optional[Dict[str, Any]] = None
    finish_reason: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "text": self.text,
            "index": self.index,
            "logprobs": self.logprobs,
            "finish_reason": self.finish_reason
        }


@dataclass
class CompletionResponse:
    """Text completion response model"""
    id: str
    created: int
    model: str
    choices: List[CompletionChoice]
    usage: Usage
    object: Literal["text_completion"] = "text_completion"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "object": self.object,
            "created": self.created,
            "model": self.model,
            "choices": [choice.to_dict() for choice in self.choices],
            "usage": self.usage.to_dict() if hasattr(self.usage, 'to_dict') else self.usage
        }


@dataclass
class CompletionStreamChoice:
    """Text completion stream choice"""
    text: str
    index: int
    logprobs: Optional[Dict[str, Any]] = None
    finish_reason: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "text": self.text,
            "index": self.index,
            "logprobs": self.logprobs,
            "finish_reason": self.finish_reason
        }


@dataclass
class CompletionStreamResponse:
    """Text completion stream response model"""
    id: str
    created: int
    model: str
    choices: List[CompletionStreamChoice]
    object: Literal["text_completion"] = "text_completion"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "object": self.object,
            "created": self.created,
            "model": self.model,
            "choices": [choice.to_dict() for choice in self.choices]
        }