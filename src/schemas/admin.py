"""
Admin API dataclass models
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Union
from datetime import datetime


# Channel models
@dataclass
class ChannelCreateRequest:
    """Channel creation request"""
    key: str
    name: str
    type: str
    endpoint: str
    api_key: str
    api_version: Optional[str] = None
    deployment_mapper: Dict[str, str] = field(default_factory=dict)
    model_pricing: Dict[str, Dict[str, float]] = field(default_factory=dict)
    enabled: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "key": self.key,
            "name": self.name,
            "type": self.type,
            "endpoint": self.endpoint,
            "api_key": self.api_key,
            "api_version": self.api_version,
            "deployment_mapper": self.deployment_mapper,
            "model_pricing": self.model_pricing,
            "enabled": self.enabled
        }


@dataclass
class ChannelUpdateRequest:
    """Channel update request"""
    name: Optional[str] = None
    type: Optional[str] = None
    endpoint: Optional[str] = None
    api_key: Optional[str] = None
    api_version: Optional[str] = None
    deployment_mapper: Optional[Dict[str, str]] = None
    model_pricing: Optional[Dict[str, Dict[str, float]]] = None
    enabled: Optional[bool] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {}
        if self.name is not None:
            result["name"] = self.name
        if self.type is not None:
            result["type"] = self.type
        if self.endpoint is not None:
            result["endpoint"] = self.endpoint
        if self.api_key is not None:
            result["api_key"] = self.api_key
        if self.api_version is not None:
            result["api_version"] = self.api_version
        if self.deployment_mapper is not None:
            result["deployment_mapper"] = self.deployment_mapper
        if self.model_pricing is not None:
            result["model_pricing"] = self.model_pricing
        if self.enabled is not None:
            result["enabled"] = self.enabled
        return result


@dataclass
class ChannelResponse:
    """Channel response"""
    key: str
    name: str
    type: str
    endpoint: str
    api_key: str
    api_version: Optional[str] = None
    deployment_mapper: Dict[str, str] = field(default_factory=dict)
    model_pricing: Dict[str, Dict[str, float]] = field(default_factory=dict)
    enabled: bool = True
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "key": self.key,
            "name": self.name,
            "type": self.type,
            "endpoint": self.endpoint,
            "api_key": self.api_key,
            "api_version": self.api_version,
            "deployment_mapper": self.deployment_mapper,
            "model_pricing": self.model_pricing,
            "enabled": self.enabled,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }


# Token models
@dataclass
class TokenCreateRequest:
    """Token creation request"""
    name: str
    key: Optional[str] = None
    channel_keys: List[str] = field(default_factory=list)
    total_quota: float = 0.0
    enabled: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "key": self.key,
            "name": self.name,
            "channel_keys": self.channel_keys,
            "total_quota": self.total_quota,
            "enabled": self.enabled
        }


@dataclass
class TokenUpdateRequest:
    """Token update request"""
    name: Optional[str] = None
    channel_keys: Optional[List[str]] = None
    total_quota: Optional[float] = None
    used_quota: Optional[float] = None
    enabled: Optional[bool] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {}
        if self.name is not None:
            result["name"] = self.name
        if self.channel_keys is not None:
            result["channel_keys"] = self.channel_keys
        if self.total_quota is not None:
            result["total_quota"] = self.total_quota
        if self.used_quota is not None:
            result["used_quota"] = self.used_quota
        if self.enabled is not None:
            result["enabled"] = self.enabled
        return result


@dataclass
class TokenResponse:
    """Token response"""
    key: str
    name: str
    channel_keys: List[str] = field(default_factory=list)
    total_quota: float = 0.0
    used_quota: float = 0.0
    remaining_quota: float = 0.0
    quota_exceeded: bool = False
    quota_usage_percent: float = 0.0
    enabled: bool = True
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "key": self.key,
            "name": self.name,
            "channel_keys": self.channel_keys,
            "total_quota": self.total_quota,
            "used_quota": self.used_quota,
            "remaining_quota": self.remaining_quota,
            "quota_exceeded": self.quota_exceeded,
            "quota_usage_percent": self.quota_usage_percent,
            "enabled": self.enabled,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }


# Usage models
@dataclass
class UsageStatsRequest:
    """Usage statistics request"""
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    token_key: Optional[str] = None
    channel_key: Optional[str] = None
    model: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {}
        if self.start_date is not None:
            result["start_date"] = self.start_date.isoformat() if hasattr(self.start_date, 'isoformat') else str(self.start_date)
        if self.end_date is not None:
            result["end_date"] = self.end_date.isoformat() if hasattr(self.end_date, 'isoformat') else str(self.end_date)
        if self.token_key is not None:
            result["token_key"] = self.token_key
        if self.channel_key is not None:
            result["channel_key"] = self.channel_key
        if self.model is not None:
            result["model"] = self.model
        return result


@dataclass
class ModelUsageStats:
    """Model usage statistics"""
    model: str
    requests: int
    tokens: int
    cost: float
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "model": self.model,
            "requests": self.requests,
            "tokens": self.tokens,
            "cost": self.cost
        }


@dataclass
class UsageStatsResponse:
    """Usage statistics response"""
    total_requests: int
    total_tokens: int
    total_cost: float
    model_breakdown: List[ModelUsageStats] = field(default_factory=list)
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "total_requests": self.total_requests,
            "total_tokens": self.total_tokens,
            "total_cost": self.total_cost,
            "model_breakdown": [stats.to_dict() for stats in self.model_breakdown],
            "start_date": self.start_date,
            "end_date": self.end_date
        }


@dataclass
class UsageLogResponse:
    """Usage log response"""
    id: int
    token_key: str
    channel_key: str
    model: str
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    input_cost: float
    output_cost: float
    total_cost: float
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    status_code: Optional[int] = None
    response_time: Optional[float] = None
    created_at: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "token_key": self.token_key,
            "channel_key": self.channel_key,
            "model": self.model,
            "prompt_tokens": self.prompt_tokens,
            "completion_tokens": self.completion_tokens,
            "total_tokens": self.total_tokens,
            "input_cost": self.input_cost,
            "output_cost": self.output_cost,
            "total_cost": self.total_cost,
            "request_id": self.request_id,
            "user_id": self.user_id,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "status_code": self.status_code,
            "response_time": self.response_time,
            "created_at": self.created_at
        }


# Settings models
@dataclass
class SettingUpdateRequest:
    """Setting update request"""
    value: Any
    
    def to_dict(self) -> Dict[str, Any]:
        return {"value": self.value}


@dataclass
class SettingResponse:
    """Setting response"""
    key: str
    value: Any
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "key": self.key,
            "value": self.value,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }


@dataclass
class GlobalPricingUpdateRequest:
    """Global pricing update request"""
    pricing: Dict[str, Dict[str, float]]
    
    def __post_init__(self):
        """Validate pricing data after initialization"""
        for model, prices in self.pricing.items():
            if not isinstance(prices, dict):
                raise ValueError(f"Pricing for {model} must be a dictionary")
            if 'input' not in prices or 'output' not in prices:
                raise ValueError(f"Pricing for {model} must include 'input' and 'output' keys")
            if not isinstance(prices['input'], (int, float)) or not isinstance(prices['output'], (int, float)):
                raise ValueError(f"Pricing values for {model} must be numbers")
    
    def to_dict(self) -> Dict[str, Any]:
        return {"pricing": self.pricing}


# System models
@dataclass
class SystemInfoResponse:
    """System information response"""
    version: str
    database_version: str
    total_channels: int
    enabled_channels: int
    total_tokens: int
    enabled_tokens: int
    total_models: int
    uptime: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "version": self.version,
            "database_version": self.database_version,
            "total_channels": self.total_channels,
            "enabled_channels": self.enabled_channels,
            "total_tokens": self.total_tokens,
            "enabled_tokens": self.enabled_tokens,
            "total_models": self.total_models,
            "uptime": self.uptime
        }


@dataclass
class DatabaseInitRequest:
    """Database initialization request"""
    force: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        return {"force": self.force}


@dataclass
class DatabaseInitResponse:
    """Database initialization response"""
    success: bool
    message: str
    tables_created: Optional[List[str]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "success": self.success,
            "message": self.message,
            "tables_created": self.tables_created
        }


# Health models
@dataclass
class HealthResponse:
    """Health check response"""
    status: str
    version: str
    timestamp: str
    database_status: Optional[str] = None
    checks: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "status": self.status,
            "version": self.version,
            "timestamp": self.timestamp,
            "database_status": self.database_status,
            "checks": self.checks
        }