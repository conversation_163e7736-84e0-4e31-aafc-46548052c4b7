"""
Chat completion models
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Union, Literal
from .common import Usage, MessageRole


@dataclass
class FunctionCall:
    """Function call information"""
    name: str
    arguments: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "arguments": self.arguments
        }


@dataclass
class ChatMessage:
    """Chat message model"""
    role: str
    content: Optional[str] = None
    name: Optional[str] = None
    function_call: Optional[FunctionCall] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {"role": self.role}
        if self.content is not None:
            result["content"] = self.content
        if self.name is not None:
            result["name"] = self.name
        if self.function_call is not None:
            result["function_call"] = self.function_call.to_dict()
        return result


@dataclass
class Function:
    """Function definition for function calling"""
    name: str
    description: Optional[str] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "name": self.name,
            "parameters": self.parameters
        }
        if self.description is not None:
            result["description"] = self.description
        return result


@dataclass
class ChatCompletionRequest:
    """Chat completion request model"""
    model: str
    messages: List[Dict[str, Any]]
    temperature: Optional[float] = 1.0
    top_p: Optional[float] = 1.0
    n: Optional[int] = 1
    stream: Optional[bool] = False
    stop: Optional[Union[str, List[str]]] = None
    max_tokens: Optional[int] = None
    presence_penalty: Optional[float] = 0.0
    frequency_penalty: Optional[float] = 0.0
    logit_bias: Optional[Dict[str, float]] = None
    user: Optional[str] = None
    functions: Optional[List[Dict[str, Any]]] = None
    function_call: Optional[Union[str, Dict[str, str]]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "model": self.model,
            "messages": self.messages
        }
        
        # Add optional fields if they have non-default values
        if self.temperature != 1.0:
            result["temperature"] = self.temperature
        if self.top_p != 1.0:
            result["top_p"] = self.top_p
        if self.n != 1:
            result["n"] = self.n
        if self.stream:
            result["stream"] = self.stream
        if self.stop is not None:
            result["stop"] = self.stop
        if self.max_tokens is not None:
            result["max_tokens"] = self.max_tokens
        if self.presence_penalty != 0.0:
            result["presence_penalty"] = self.presence_penalty
        if self.frequency_penalty != 0.0:
            result["frequency_penalty"] = self.frequency_penalty
        if self.logit_bias is not None:
            result["logit_bias"] = self.logit_bias
        if self.user is not None:
            result["user"] = self.user
        if self.functions is not None:
            result["functions"] = self.functions
        if self.function_call is not None:
            result["function_call"] = self.function_call
            
        return result


@dataclass
class ChatCompletionChoice:
    """Chat completion choice"""
    index: int
    message: Dict[str, Any]
    finish_reason: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "index": self.index,
            "message": self.message
        }
        if self.finish_reason is not None:
            result["finish_reason"] = self.finish_reason
        return result


@dataclass
class ChatCompletionResponse:
    """Chat completion response model"""
    id: str
    object: str = "chat.completion"
    created: int = 0
    model: str = ""
    choices: List[Dict[str, Any]] = field(default_factory=list)
    usage: Optional[Dict[str, Any]] = None
    system_fingerprint: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "id": self.id,
            "object": self.object,
            "created": self.created,
            "model": self.model,
            "choices": self.choices
        }
        if self.usage is not None:
            result["usage"] = self.usage
        if self.system_fingerprint is not None:
            result["system_fingerprint"] = self.system_fingerprint
        return result


@dataclass
class ChatCompletionStreamChoice:
    """Chat completion stream choice"""
    index: int
    delta: Dict[str, Any]
    finish_reason: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "index": self.index,
            "delta": self.delta
        }
        if self.finish_reason is not None:
            result["finish_reason"] = self.finish_reason
        return result


@dataclass
class ChatCompletionStreamResponse:
    """Chat completion stream response model"""
    id: str
    object: str = "chat.completion.chunk"
    created: int = 0
    model: str = ""
    choices: List[Dict[str, Any]] = field(default_factory=list)
    system_fingerprint: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "id": self.id,
            "object": self.object,
            "created": self.created,
            "model": self.model,
            "choices": self.choices
        }
        if self.system_fingerprint is not None:
            result["system_fingerprint"] = self.system_fingerprint
        return result