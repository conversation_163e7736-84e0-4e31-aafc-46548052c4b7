"""
Database operations for Cloudflare D1
"""

import json
from typing import Dict, Any, Optional, List
from src.models import ChannelConfig, ApiTokenData, DatabaseRecord
from src.constants import DB_VERSION, SETTINGS_KEYS, DEFAULT_PRICING
from src.utils import parse_json_safe


class DatabaseManager:
    """Manages database operations for Cloudflare D1"""
    
    def __init__(self, db_binding):
        self.db = db_binding
    
    async def initialize(self) -> bool:
        """Initialize database tables"""
        try:
            # Create tables if they don't exist
            await self._create_tables()
            
            # Check and update database version
            current_version = await self.get_setting(SETTINGS_KEYS["DB_VERSION"])
            if current_version != DB_VERSION:
                await self._migrate_database(current_version)
                await self.set_setting(SETTINGS_KEYS["DB_VERSION"], DB_VERSION)
            
            # Initialize default pricing if not exists
            pricing = await self.get_setting(SETTINGS_KEYS["GLOBAL_PRICING"])
            if not pricing:
                await self.set_setting(SETTINGS_KEYS["GLOBAL_PRICING"], DEFAULT_PRICING)
            
            return True
        except Exception as e:
            print(f"Database initialization error: {str(e)}")
            return False
    
    async def _create_tables(self):
        """Create database tables"""
        # Channel configuration table
        await self.db.prepare("""
            CREATE TABLE IF NOT EXISTS channel_config (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL
            )
        """).run()
        
        # API tokens table
        await self.db.prepare("""
            CREATE TABLE IF NOT EXISTS api_token (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                usage REAL DEFAULT 0.0
            )
        """).run()
        
        # Settings table
        await self.db.prepare("""
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL
            )
        """).run()
    
    async def _migrate_database(self, from_version: Optional[str]):
        """Handle database migrations"""
        # Add migration logic here if needed in the future
        print(f"Migrating database from version {from_version} to {DB_VERSION}")
    
    # Settings operations
    async def get_setting(self, key: str, default: Any = None) -> Any:
        """Get a setting value"""
        try:
            result = await self.db.prepare(
                "SELECT value FROM settings WHERE key = ?"
            ).bind(key).first()
            
            if result:
                return parse_json_safe(result.value) or result.value
            return default
        except:
            return default
    
    async def set_setting(self, key: str, value: Any):
        """Set a setting value"""
        value_str = json.dumps(value) if isinstance(value, (dict, list)) else str(value)
        await self.db.prepare(
            "INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)"
        ).bind(key, value_str).run()
    
    # Channel operations
    async def get_channel(self, key: str) -> Optional[ChannelConfig]:
        """Get a channel configuration"""
        try:
            result = await self.db.prepare(
                "SELECT value FROM channel_config WHERE key = ?"
            ).bind(key).first()
            
            if result:
                data = parse_json_safe(result.value)
                if data:
                    return ChannelConfig.from_dict(data)
            return None
        except Exception as e:
            print(f"Error getting channel {key}: {str(e)}")
            return None
    
    async def get_all_channels(self) -> Dict[str, ChannelConfig]:
        """Get all channel configurations"""
        try:
            results = await self.db.prepare(
                "SELECT key, value FROM channel_config"
            ).all()
            
            channels = {}
            for result in results:
                data = parse_json_safe(result.value)
                if data:
                    channels[result.key] = ChannelConfig.from_dict(data)
            
            return channels
        except Exception as e:
            print(f"Error getting all channels: {str(e)}")
            return {}
    
    async def create_channel(self, key: str, config: ChannelConfig) -> bool:
        """Create a new channel"""
        try:
            await self.db.prepare(
                "INSERT INTO channel_config (key, value) VALUES (?, ?)"
            ).bind(key, json.dumps(config.to_dict())).run()
            return True
        except Exception as e:
            print(f"Error creating channel {key}: {str(e)}")
            return False
    
    async def update_channel(self, key: str, config: ChannelConfig) -> bool:
        """Update an existing channel"""
        try:
            await self.db.prepare(
                "UPDATE channel_config SET value = ? WHERE key = ?"
            ).bind(json.dumps(config.to_dict()), key).run()
            return True
        except Exception as e:
            print(f"Error updating channel {key}: {str(e)}")
            return False
    
    async def delete_channel(self, key: str) -> bool:
        """Delete a channel"""
        try:
            await self.db.prepare(
                "DELETE FROM channel_config WHERE key = ?"
            ).bind(key).run()
            return True
        except Exception as e:
            print(f"Error deleting channel {key}: {str(e)}")
            return False
    
    # Token operations
    async def get_api_token(self, token: str) -> Optional[ApiTokenData]:
        """Get API token data"""
        try:
            result = await self.db.prepare(
                "SELECT value, usage FROM api_token WHERE key = ?"
            ).bind(token).first()
            
            if result:
                data = parse_json_safe(result.value)
                if data:
                    token_data = ApiTokenData.from_dict(data)
                    token_data.used_quota = result.usage or 0.0
                    return token_data
            return None
        except Exception as e:
            print(f"Error getting token {token}: {str(e)}")
            return None
    
    async def get_all_tokens(self) -> Dict[str, ApiTokenData]:
        """Get all API tokens"""
        try:
            results = await self.db.prepare(
                "SELECT key, value, usage FROM api_token"
            ).all()
            
            tokens = {}
            for result in results:
                data = parse_json_safe(result.value)
                if data:
                    token_data = ApiTokenData.from_dict(data)
                    token_data.used_quota = result.usage or 0.0
                    tokens[result.key] = token_data
            
            return tokens
        except Exception as e:
            print(f"Error getting all tokens: {str(e)}")
            return {}
    
    async def create_token(self, token: str, config: ApiTokenData) -> bool:
        """Create a new API token"""
        try:
            await self.db.prepare(
                "INSERT INTO api_token (key, value, usage) VALUES (?, ?, ?)"
            ).bind(token, json.dumps(config.to_dict()), 0.0).run()
            return True
        except Exception as e:
            print(f"Error creating token {token}: {str(e)}")
            return False
    
    async def update_token(self, token: str, config: ApiTokenData) -> bool:
        """Update an existing token"""
        try:
            await self.db.prepare(
                "UPDATE api_token SET value = ? WHERE key = ?"
            ).bind(json.dumps(config.to_dict()), token).run()
            return True
        except Exception as e:
            print(f"Error updating token {token}: {str(e)}")
            return False
    
    async def delete_token(self, token: str) -> bool:
        """Delete an API token"""
        try:
            await self.db.prepare(
                "DELETE FROM api_token WHERE key = ?"
            ).bind(token).run()
            return True
        except Exception as e:
            print(f"Error deleting token {token}: {str(e)}")
            return False
    
    async def update_token_usage(self, token: str, cost: float) -> bool:
        """Update token usage cost"""
        try:
            await self.db.prepare(
                "UPDATE api_token SET usage = usage + ? WHERE key = ?"
            ).bind(cost, token).run()
            return True
        except Exception as e:
            print(f"Error updating token usage {token}: {str(e)}")
            return False
    
    # Utility methods
    async def channel_exists(self, key: str) -> bool:
        """Check if channel exists"""
        channel = await self.get_channel(key)
        return channel is not None
    
    async def token_exists(self, token: str) -> bool:
        """Check if token exists"""
        token_data = await self.get_api_token(token)
        return token_data is not None
    
    async def get_channels_for_token(self, token_data: ApiTokenData) -> List[str]:
        """Get available channel keys for a token"""
        if token_data.channel_keys:
            # Token has specific channel permissions
            available_channels = []
            for key in token_data.channel_keys:
                if await self.channel_exists(key):
                    available_channels.append(key)
            return available_channels
        else:
            # Token has access to all channels
            all_channels = await self.get_all_channels()
            return list(all_channels.keys())