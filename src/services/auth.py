"""
Authentication service for dependency injection
"""

import logging
from src.cfastapi import HTTPException, Depends
from src.models import ApiTokenData
from src.services.database import get_database_manager
from src.database import DatabaseManager

logger = logging.getLogger(__name__)


async def get_current_token(
    request, 
    db: DatabaseManager = Depends(get_database_manager)
) -> ApiTokenData:
    """Get and validate current API token"""
    auth_header = request.headers.get('authorization', '')
    if not auth_header.startswith('Bearer '):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    token = auth_header[7:]  # Remove 'Bearer ' prefix
    
    # Get token from database
    token_data = await db.get_api_token(token)
    if not token_data:
        raise HTTPException(status_code=401, detail="Invalid API token")
    
    # Check if token is enabled
    if not token_data.enabled:
        raise HTTPException(status_code=401, detail="Token is disabled")
    
    # Check quota
    if token_data.quota_exceeded:
        raise HTTPException(
            status_code=429,
            detail=f"Quota exceeded. Used: ${token_data.used_quota:.4f}, Limit: ${token_data.total_quota:.4f}"
        )
    
    logger.info(f"Token authenticated: {token[:8]}... ({token_data.name})")
    return token_data


async def get_admin_token(request, app) -> str:
    """Verify admin token"""
    env = app.state.get('env')
    if not env:
        raise HTTPException(status_code=500, detail="Environment not available")
    
    # Get admin token from env
    try:
        admin_token = env.ADMIN_TOKEN if hasattr(env, 'ADMIN_TOKEN') else env.get('ADMIN_TOKEN')
    except Exception:
        admin_token = env.get('ADMIN_TOKEN') if hasattr(env, 'get') else None
    
    if not admin_token:
        raise HTTPException(status_code=500, detail="Admin token not configured")
    
    # Check authorization header
    auth_header = request.headers.get('authorization', '')
    if not auth_header.startswith('Bearer '):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    token = auth_header[7:]  # Remove 'Bearer ' prefix
    
    if token != admin_token:
        raise HTTPException(status_code=401, detail="Invalid admin token")
    
    logger.info("Admin token authenticated")
    return token