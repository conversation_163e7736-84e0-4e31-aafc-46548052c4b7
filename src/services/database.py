"""
Database service for dependency injection
"""

from src.database import DatabaseManager
from src.cfastapi import HTTPException


async def get_database_manager(app) -> DatabaseManager:
    """Get database manager dependency"""
    env = app.state.get('env')
    if not env:
        raise HTTPException(status_code=500, detail="Environment not available")
    
    # Get DB binding from env
    try:
        db_binding = env.DB if hasattr(env, 'DB') else env.get('DB')
    except Exception:
        db_binding = env.get('DB') if hasattr(env, 'get') else None
    
    if not db_binding:
        raise HTTPException(status_code=500, detail="Database binding not available")
    
    return DatabaseManager(db_binding)