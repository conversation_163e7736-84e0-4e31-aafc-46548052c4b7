"""
CFastAPI middleware system
"""

from typing import Callable, List, Optional
from workers import Response
import json


class BaseMiddleware:
    """Base middleware class"""
    
    async def process(self, request, call_next: Callable) -> Response:
        """Process request through middleware"""
        return await call_next(request)


class CORSMiddleware(BaseMiddleware):
    """CORS middleware for handling cross-origin requests"""
    
    def __init__(
        self,
        allow_origins: List[str] = None,
        allow_methods: List[str] = None,
        allow_headers: List[str] = None,
        allow_credentials: bool = False,
        expose_headers: List[str] = None,
        max_age: int = 600
    ):
        self.allow_origins = allow_origins or ["*"]
        self.allow_methods = allow_methods or ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"]
        self.allow_headers = allow_headers or ["*"]
        self.allow_credentials = allow_credentials
        self.expose_headers = expose_headers or []
        self.max_age = max_age
    
    async def process(self, request, call_next: Callable) -> Response:
        """Process CORS headers"""
        origin = request.headers.get("origin", "")
        
        # Handle preflight OPTIONS requests
        if request.method == "OPTIONS":
            return self._create_preflight_response(origin)
        
        # Process normal request
        response = await call_next(request)
        
        # Add CORS headers
        headers = dict(response.headers) if hasattr(response, 'headers') else {}
        
        # Set Access-Control-Allow-Origin
        if "*" in self.allow_origins:
            headers["Access-Control-Allow-Origin"] = "*"
        elif origin in self.allow_origins:
            headers["Access-Control-Allow-Origin"] = origin
        
        # Set other CORS headers
        if self.allow_credentials:
            headers["Access-Control-Allow-Credentials"] = "true"
        
        if self.expose_headers:
            headers["Access-Control-Expose-Headers"] = ", ".join(self.expose_headers)
        
        # Create new response with CORS headers
        return Response(
            response.body if hasattr(response, 'body') else await response.text(),
            status=response.status,
            headers=headers
        )
    
    def _create_preflight_response(self, origin: str) -> Response:
        """Create preflight response for OPTIONS requests"""
        headers = {}
        
        # Set Access-Control-Allow-Origin
        if "*" in self.allow_origins:
            headers["Access-Control-Allow-Origin"] = "*"
        elif origin in self.allow_origins:
            headers["Access-Control-Allow-Origin"] = origin
        
        # Set allowed methods
        headers["Access-Control-Allow-Methods"] = ", ".join(self.allow_methods)
        
        # Set allowed headers
        if "*" in self.allow_headers:
            headers["Access-Control-Allow-Headers"] = "*"
        else:
            headers["Access-Control-Allow-Headers"] = ", ".join(self.allow_headers)
        
        # Set credentials
        if self.allow_credentials:
            headers["Access-Control-Allow-Credentials"] = "true"
        
        # Set max age
        headers["Access-Control-Max-Age"] = str(self.max_age)
        
        return Response("", status=204, headers=headers)


class RequestLoggingMiddleware(BaseMiddleware):
    """Request logging middleware"""
    
    def __init__(self, include_body: bool = False):
        self.include_body = include_body
    
    async def process(self, request, call_next: Callable) -> Response:
        """Log request details"""
        import time
        from js import URL
        
        # Get request info
        url = URL.new(request.url)
        method = request.method
        path = url.pathname
        
        # Log request start
        start_time = time.time()
        print(f"[REQUEST] {method} {path}")
        
        if self.include_body and method in ('POST', 'PUT', 'PATCH'):
            try:
                body = await request.json()
                print(f"[REQUEST BODY] {json.dumps(body)}")
            except:
                pass
        
        # Process request
        response = await call_next(request)
        
        # Log response
        duration = time.time() - start_time
        print(f"[RESPONSE] {response.status} - {duration:.3f}s")
        
        return response


class AuthenticationMiddleware(BaseMiddleware):
    """Authentication middleware"""
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
    
    async def process(self, request, call_next: Callable) -> Response:
        """Process authentication"""
        # This is a placeholder - implement actual authentication logic
        # You can check JWT tokens, API keys, etc.
        
        auth_header = request.headers.get("authorization", "")
        
        # Skip authentication for certain paths
        from js import URL
        url = URL.new(request.url)
        path = url.pathname
        
        # Skip auth for health checks, docs, etc.
        if path in ["/health", "/", "/api/docs", "/api/redocs", "/api/openapi.json"]:
            return await call_next(request)
        
        # Add authentication logic here
        # For now, just pass through
        return await call_next(request)


class RateLimitMiddleware(BaseMiddleware):
    """Rate limiting middleware"""
    
    def __init__(self, requests_per_minute: int = 60):
        self.requests_per_minute = requests_per_minute
        self.request_counts = {}  # In production, use external storage
    
    async def process(self, request, call_next: Callable) -> Response:
        """Process rate limiting"""
        import time
        
        # Get client IP
        client_ip = request.headers.get("cf-connecting-ip", 
                   request.headers.get("x-forwarded-for", "unknown"))
        
        current_time = int(time.time() / 60)  # Current minute
        key = f"{client_ip}:{current_time}"
        
        # Check rate limit
        current_count = self.request_counts.get(key, 0)
        if current_count >= self.requests_per_minute:
            return Response(
                json.dumps({"error": "Rate limit exceeded"}),
                status=429,
                headers={"Content-Type": "application/json"}
            )
        
        # Increment counter
        self.request_counts[key] = current_count + 1
        
        # Clean old entries
        self._cleanup_old_entries(current_time)
        
        return await call_next(request)
    
    def _cleanup_old_entries(self, current_time: int):
        """Clean up old rate limit entries"""
        keys_to_remove = [
            key for key in self.request_counts.keys()
            if int(key.split(':')[1]) < current_time - 5  # Keep last 5 minutes
        ]
        for key in keys_to_remove:
            del self.request_counts[key]