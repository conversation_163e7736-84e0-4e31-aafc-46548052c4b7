"""
CFastAPI dependency injection system
"""

import json
import inspect
from typing import Dict, Any, Callable, Optional, Type, get_type_hints, Union
from js import URL

from .exceptions import ValidationError, HTTPException
from ..schemas.common import validate_chat_request


class Depends:
    """Dependency marker class"""
    
    def __init__(self, dependency: Callable, use_cache: bool = True):
        self.dependency = dependency
        self.use_cache = use_cache


async def solve_dependencies(
    signature: inspect.Signature,
    type_hints: Dict[str, Type],
    request,
    app,
    path_params: Dict[str, str] = None
) -> Dict[str, Any]:
    """
    Solve dependencies for a route handler
    
    Args:
        signature: Function signature
        type_hints: Type hints from function
        request: Cloudflare Workers Request object
        app: CFastAPI app instance
        path_params: Path parameters from URL
    
    Returns:
        Dictionary of resolved dependencies
    """
    path_params = path_params or {}
    resolved = {}
    
    # Cache for dependency results
    dependency_cache = app.state.get('_dependency_cache', {})
    app.state['_dependency_cache'] = dependency_cache
    
    url = URL.new(request.url)
    query_params = _parse_query_params(url.search)
    
    # Get request body if present
    request_body = None
    if request.method in ('POST', 'PUT', 'PATCH'):
        try:
            request_body = await request.json()
        except:
            request_body = None
    
    for param_name, param in signature.parameters.items():
        param_type = type_hints.get(param_name, type(param.default))
        
        # Handle Depends() dependencies
        if isinstance(param.default, Depends):
            dependency_result = await _resolve_dependency(
                param.default,
                request,
                app,
                path_params,
                query_params,
                request_body,
                dependency_cache
            )
            resolved[param_name] = dependency_result
            continue
        
        # Handle path parameters
        if param_name in path_params:
            resolved[param_name] = _convert_param(path_params[param_name], param_type)
            continue
        
        # Handle query parameters
        if param_name in query_params:
            resolved[param_name] = _convert_param(query_params[param_name], param_type)
            continue
        
        # Handle request body - simple validation for chat requests
        if request_body is not None and param_name in ['request_data', 'data', 'body']:
            # Validate chat completion requests
            if request.url.endswith('/chat/completions') or request.url.endswith('/completions'):
                try:
                    validated_data = validate_chat_request(request_body)
                    resolved[param_name] = validated_data
                except ValueError as e:
                    raise ValidationError(f"Request validation failed: {e}")
            else:
                resolved[param_name] = request_body
            continue
        
        # Handle special parameters
        if param_name == 'request':
            resolved[param_name] = request
            continue
        elif param_name == 'app':
            resolved[param_name] = app
            continue
        
        # Handle default values
        if param.default != inspect.Parameter.empty:
            resolved[param_name] = param.default
            continue
        
        # Required parameter not found
        if param.default == inspect.Parameter.empty:
            raise HTTPException(
                status_code=422,
                detail=f"Missing required parameter: {param_name}"
            )
    
    return resolved


async def _resolve_dependency(
    depends: Depends,
    request,
    app,
    path_params: Dict[str, str],
    query_params: Dict[str, str],
    request_body: Optional[Dict[str, Any]],
    dependency_cache: Dict[str, Any]
) -> Any:
    """Resolve a single dependency"""
    dependency_func = depends.dependency
    
    # Check cache
    cache_key = f"{id(dependency_func)}"
    if depends.use_cache and cache_key in dependency_cache:
        return dependency_cache[cache_key]
    
    # Get dependency signature
    dep_signature = inspect.signature(dependency_func)
    dep_type_hints = get_type_hints(dependency_func)
    
    # Recursively solve sub-dependencies
    dep_kwargs = await solve_dependencies(
        dep_signature,
        dep_type_hints,
        request,
        app,
        path_params
    )
    
    # Call dependency function
    if inspect.iscoroutinefunction(dependency_func):
        result = await dependency_func(**dep_kwargs)
    else:
        result = dependency_func(**dep_kwargs)
    
    # Cache result
    if depends.use_cache:
        dependency_cache[cache_key] = result
    
    return result


def _parse_query_params(search_string: str) -> Dict[str, str]:
    """Parse URL query parameters"""
    if not search_string or search_string == '?':
        return {}
    
    # Remove leading '?'
    if search_string.startswith('?'):
        search_string = search_string[1:]
    
    params = {}
    for pair in search_string.split('&'):
        if '=' in pair:
            key, value = pair.split('=', 1)
            # URL decode
            key = key.replace('%20', ' ').replace('+', ' ')
            value = value.replace('%20', ' ').replace('+', ' ')
            params[key] = value
    
    return params


def _convert_param(value: str, param_type: Type) -> Any:
    """Convert string parameter to appropriate type"""
    if param_type == str or param_type == type(None):
        return value
    elif param_type == int:
        try:
            return int(value)
        except ValueError:
            raise ValidationError(f"Invalid integer value: {value}")
    elif param_type == float:
        try:
            return float(value)
        except ValueError:
            raise ValidationError(f"Invalid float value: {value}")
    elif param_type == bool:
        if value.lower() in ('true', '1', 'yes', 'on'):
            return True
        elif value.lower() in ('false', '0', 'no', 'off'):
            return False
        else:
            raise ValidationError(f"Invalid boolean value: {value}")
    else:
        return value