"""
CFastAPI routing system
"""

import re
import json
import inspect
from typing import Dict, Any, Callable, Optional, List, Union, Type, get_type_hints
from workers import Response
from js import URL

from .depends import solve_dependencies
from .exceptions import HTTPException, ValidationError


class Route:
    """Individual route handler"""
    
    def __init__(
        self,
        path: str,
        method: str,
        handler: Callable,
        response_model: Optional[Type] = None,
        status_code: int = 200,
        tags: Optional[List[str]] = None,
        summary: Optional[str] = None,
        description: Optional[str] = None
    ):
        self.path = path
        self.method = method.upper()
        self.handler = handler
        self.response_model = response_model
        self.status_code = status_code
        self.tags = tags or []
        self.summary = summary
        self.description = description
        
        # Compile path pattern for matching
        self.path_pattern, self.path_params = self._compile_path(path)
        
        # Analyze handler signature
        self.signature = inspect.signature(handler)
        self.type_hints = get_type_hints(handler)
    
    def _compile_path(self, path: str):
        """Compile path pattern for matching and extract parameter names"""
        # Convert FastAPI-style path parameters to regex
        # e.g., "/users/{user_id}" -> r"/users/(?P<user_id>[^/]+)"
        
        param_pattern = re.compile(r'\{([^}]+)\}')
        path_params = []
        
        def replace_param(match):
            param_name = match.group(1)
            path_params.append(param_name)
            return f'(?P<{param_name}>[^/]+)'
        
        pattern = param_pattern.sub(replace_param, path)
        pattern = f'^{pattern}$'
        
        return re.compile(pattern), path_params
    
    def matches(self, method: str, path: str) -> Optional[Dict[str, str]]:
        """Check if route matches request method and path"""
        if self.method != method.upper():
            return None
        
        match = self.path_pattern.match(path)
        if match:
            return match.groupdict()
        return None
    
    async def call(self, request, app, path_params: Dict[str, str] = None) -> Response:
        """Execute route handler with dependency injection"""
        path_params = path_params or {}
        
        # Solve dependencies
        kwargs = await solve_dependencies(
            self.signature,
            self.type_hints,
            request,
            app,
            path_params
        )
        
        # Call handler
        if inspect.iscoroutinefunction(self.handler):
            result = await self.handler(**kwargs)
        else:
            result = self.handler(**kwargs)
        
        # Process response
        return self._create_response(result)
    
    def _create_response(self, result: Any) -> Response:
        """Create response from handler result"""
        if isinstance(result, Response):
            return result
        
        # Handle dataclass objects with to_dict() method
        if hasattr(result, 'to_dict') and callable(getattr(result, 'to_dict')):
            data = result.to_dict()
        # Handle dict-like objects
        elif isinstance(result, dict):
            data = result
        # Handle lists
        elif isinstance(result, list):
            # Convert dataclass objects in list to dicts
            data = []
            for item in result:
                if hasattr(item, 'to_dict') and callable(getattr(item, 'to_dict')):
                    data.append(item.to_dict())
                else:
                    data.append(item)
        else:
            data = result
        
        return Response(
            json.dumps(data, ensure_ascii=False) if data is not None else "",
            status=self.status_code,
            headers={
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*"
            }
        )


class Router:
    """Router for managing routes"""
    
    def __init__(self, prefix: str = "", tags: Optional[List[str]] = None):
        self.prefix = prefix
        self.tags = tags or []
        self.routes: List[Route] = []
        self.sub_routers: List['Router'] = []
    
    def add_route(
        self,
        path: str,
        method: str,
        handler: Callable,
        **kwargs
    ) -> Route:
        """Add a route to the router"""
        full_path = self.prefix + path
        route = Route(full_path, method, handler, **kwargs)
        
        # Add router tags to route
        if self.tags:
            route.tags.extend(self.tags)
        
        self.routes.append(route)
        return route
    
    def get(self, path: str, **kwargs):
        """Register GET route"""
        def decorator(func):
            self.add_route(path, "GET", func, **kwargs)
            return func
        return decorator
    
    def post(self, path: str, **kwargs):
        """Register POST route"""
        def decorator(func):
            self.add_route(path, "POST", func, **kwargs)
            return func
        return decorator
    
    def put(self, path: str, **kwargs):
        """Register PUT route"""
        def decorator(func):
            self.add_route(path, "PUT", func, **kwargs)
            return func
        return decorator
    
    def delete(self, path: str, **kwargs):
        """Register DELETE route"""
        def decorator(func):
            self.add_route(path, "DELETE", func, **kwargs)
            return func
        return decorator
    
    def patch(self, path: str, **kwargs):
        """Register PATCH route"""
        def decorator(func):
            self.add_route(path, "PATCH", func, **kwargs)
            return func
        return decorator
    
    def options(self, path: str, **kwargs):
        """Register OPTIONS route"""
        def decorator(func):
            self.add_route(path, "OPTIONS", func, **kwargs)
            return func
        return decorator
    
    def head(self, path: str, **kwargs):
        """Register HEAD route"""
        def decorator(func):
            self.add_route(path, "HEAD", func, **kwargs)
            return func
        return decorator
    
    def include_router(self, router: 'Router', prefix: str = "", tags: List[str] = None):
        """Include a sub-router"""
        sub_router = Router(self.prefix + prefix, tags)
        sub_router.routes = router.routes.copy()
        sub_router.sub_routers = router.sub_routers.copy()
        
        # Update route paths with new prefix
        for route in sub_router.routes:
            route.path = self.prefix + prefix + route.path[len(router.prefix):]
            route.path_pattern, route.path_params = route._compile_path(route.path)
            
            # Add tags
            if tags:
                route.tags.extend(tags)
        
        self.sub_routers.append(sub_router)
    
    async def handle(self, request, app) -> Response:
        """Handle incoming request"""
        url = URL.new(request.url)
        method = request.method
        path = url.pathname
        
        # Try to match route in this router
        for route in self.routes:
            path_params = route.matches(method, path)
            if path_params is not None:
                return await route.call(request, app, path_params)
        
        # Try sub-routers
        for sub_router in self.sub_routers:
            try:
                return await sub_router.handle(request, app)
            except HTTPException as e:
                if e.status_code != 404:
                    raise
                continue
        
        # No route found
        raise HTTPException(status_code=404, detail="Not Found")