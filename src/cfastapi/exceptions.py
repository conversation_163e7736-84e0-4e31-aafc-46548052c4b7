"""
CFastAPI exception handling
"""

from typing import Dict, Any, Optional, Union


class CFastAPIException(Exception):
    """Base exception for CFastAPI"""
    pass


class HTTPException(CFastAPIException):
    """HTTP exception with status code and detail"""
    
    def __init__(
        self,
        status_code: int,
        detail: Union[str, Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ):
        self.status_code = status_code
        self.detail = detail
        self.headers = headers
        super().__init__(detail)


class ValidationError(CFastAPIException):
    """Request validation error"""
    
    def __init__(self, detail: str):
        self.detail = detail
        super().__init__(detail)


class AuthenticationError(HTTPException):
    """Authentication error (401)"""
    
    def __init__(self, detail: str = "Authentication required"):
        super().__init__(status_code=401, detail=detail)


class AuthorizationError(HTTPException):
    """Authorization error (403)"""
    
    def __init__(self, detail: str = "Access forbidden"):
        super().__init__(status_code=403, detail=detail)


class NotFoundError(HTTPException):
    """Not found error (404)"""
    
    def __init__(self, detail: str = "Resource not found"):
        super().__init__(status_code=404, detail=detail)


class ConflictError(HTTPException):
    """Conflict error (409)"""
    
    def __init__(self, detail: str = "Resource conflict"):
        super().__init__(status_code=409, detail=detail)


class QuotaExceededError(HTTPException):
    """Quota exceeded error (429)"""
    
    def __init__(self, detail: str = "Quota exceeded"):
        super().__init__(status_code=429, detail=detail)


class InternalServerError(HTTPException):
    """Internal server error (500)"""
    
    def __init__(self, detail: str = "Internal server error"):
        super().__init__(status_code=500, detail=detail)


class ServiceUnavailableError(HTTPException):
    """Service unavailable error (503)"""
    
    def __init__(self, detail: str = "Service unavailable"):
        super().__init__(status_code=503, detail=detail)