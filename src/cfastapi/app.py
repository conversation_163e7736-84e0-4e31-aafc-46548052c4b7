"""
CFastAPI - Main application class for Cloudflare Workers
"""

import json
import traceback
from typing import Dict, Any, Callable, Optional, List, Union
from workers import Response
from js import URL
import inspect
import re

from .routing import Route, Router
from .depends import solve_dependencies
from .exceptions import HTTPException, ValidationError
from .middleware import BaseMiddleware


class CFastAPI:
    """
    FastAPI-style application for Cloudflare Workers Python
    """
    
    def __init__(
        self,
        title: str = "CFastAPI",
        description: str = "",
        version: str = "1.0.0",
        debug: bool = False
    ):
        self.title = title
        self.description = description
        self.version = version
        self.debug = debug
        
        # Router for handling routes
        self.router = Router()
        
        # Middleware stack
        self.middleware: List[BaseMiddleware] = []
        
        # Startup and shutdown handlers
        self.startup_handlers: List[Callable] = []
        self.shutdown_handlers: List[Callable] = []
        
        # Application state
        self.state = {}
        
        # Initialize flag
        self._initialized = False
    
    def add_middleware(self, middleware_class: type, **kwargs):
        """Add middleware to the application"""
        middleware = middleware_class(**kwargs)
        self.middleware.append(middleware)
    
    def on_event(self, event_type: str):
        """Decorator for startup/shutdown events"""
        def decorator(func: Callable):
            if event_type == "startup":
                self.startup_handlers.append(func)
            elif event_type == "shutdown":
                self.shutdown_handlers.append(func)
            return func
        return decorator
    
    def get(self, path: str, **kwargs):
        """Register GET route"""
        return self.router.get(path, **kwargs)
    
    def post(self, path: str, **kwargs):
        """Register POST route"""
        return self.router.post(path, **kwargs)
    
    def put(self, path: str, **kwargs):
        """Register PUT route"""
        return self.router.put(path, **kwargs)
    
    def delete(self, path: str, **kwargs):
        """Register DELETE route"""
        return self.router.delete(path, **kwargs)
    
    def patch(self, path: str, **kwargs):
        """Register PATCH route"""
        return self.router.patch(path, **kwargs)
    
    def options(self, path: str, **kwargs):
        """Register OPTIONS route"""
        return self.router.options(path, **kwargs)
    
    def head(self, path: str, **kwargs):
        """Register HEAD route"""
        return self.router.head(path, **kwargs)
    
    def include_router(self, router: Router, prefix: str = "", tags: List[str] = None):
        """Include a sub-router"""
        self.router.include_router(router, prefix, tags)
    
    async def _run_startup_handlers(self):
        """Run startup event handlers"""
        for handler in self.startup_handlers:
            if inspect.iscoroutinefunction(handler):
                await handler()
            else:
                handler()
    
    async def _process_middleware(self, request, call_next):
        """Process middleware stack"""
        async def process_layer(middlewares, index=0):
            if index >= len(middlewares):
                return await call_next(request)
            
            middleware = middlewares[index]
            return await middleware.process(request, lambda req: process_layer(middlewares, index + 1))
        
        return await process_layer(self.middleware)
    
    async def handle(self, request, env: Dict[str, Any], context=None) -> Response:
        """
        Main request handler for Cloudflare Workers
        
        Args:
            request: Cloudflare Workers Request object
            env: Environment bindings (DB, ASSETS, etc.)
            context: Worker context
        """
        try:
            # Initialize on first request
            if not self._initialized:
                await self._run_startup_handlers()
                self._initialized = True
            
            # Store env in app state for access in dependencies
            self.state['env'] = env
            self.state['context'] = context
            
            # Process request through middleware and router
            async def route_handler(req):
                return await self.router.handle(req, self)
            
            if self.middleware:
                response = await self._process_middleware(request, route_handler)
            else:
                response = await route_handler(request)
            
            return response
            
        except HTTPException as e:
            return self._create_error_response(e.status_code, e.detail)
        except ValidationError as e:
            return self._create_error_response(422, {"error": "Validation failed", "details": str(e)})
        except Exception as e:
            error_details = {
                "error": str(e),
                "type": type(e).__name__
            }
            
            if self.debug:
                error_details["traceback"] = traceback.format_exc()
            
            return self._create_error_response(500, error_details)
    
    def _create_error_response(self, status_code: int, detail: Union[str, Dict[str, Any]]) -> Response:
        """Create error response"""
        if isinstance(detail, str):
            error_data = {"error": detail}
        else:
            error_data = detail
        
        return Response(
            json.dumps(error_data, ensure_ascii=False),
            status=status_code,
            headers={
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*"
            }
        )
    
    def _create_success_response(self, data: Any, status_code: int = 200) -> Response:
        """Create success response"""
        return Response(
            json.dumps(data, ensure_ascii=False) if data is not None else "",
            status=status_code,
            headers={
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*"
            }
        )