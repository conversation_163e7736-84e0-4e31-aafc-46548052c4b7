"""
Data models and type definitions
"""

from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import json


@dataclass
class Usage:
    """Token usage information"""
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    
    def to_dict(self) -> Dict[str, int]:
        return {
            "prompt_tokens": self.prompt_tokens,
            "completion_tokens": self.completion_tokens,
            "total_tokens": self.total_tokens
        }


@dataclass
class ChannelConfig:
    """Channel configuration for AI providers"""
    name: str
    type: str  # "openai" or "azure-openai"
    endpoint: str
    api_key: str
    api_version: Optional[str] = None  # Required for Azure OpenAI
    deployment_mapper: Dict[str, str] = None  # Model name to deployment mapping
    model_pricing: Dict[str, Dict[str, float]] = None  # Custom pricing per model
    enabled: bool = True
    
    def __post_init__(self):
        if self.deployment_mapper is None:
            self.deployment_mapper = {}
        if self.model_pricing is None:
            self.model_pricing = {}
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "type": self.type,
            "endpoint": self.endpoint,
            "api_key": self.api_key,
            "api_version": self.api_version,
            "deployment_mapper": self.deployment_mapper,
            "model_pricing": self.model_pricing,
            "enabled": self.enabled
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChannelConfig':
        return cls(
            name=data.get("name", ""),
            type=data.get("type", ""),
            endpoint=data.get("endpoint", ""),
            api_key=data.get("api_key", ""),
            api_version=data.get("api_version"),
            deployment_mapper=data.get("deployment_mapper", {}),
            model_pricing=data.get("model_pricing", {}),
            enabled=data.get("enabled", True)
        )


@dataclass
class ApiTokenData:
    """API token configuration and metadata"""
    name: str
    channel_keys: List[str] = None  # Empty list means access to all channels
    total_quota: float = 0.0  # Total quota in dollars
    used_quota: float = 0.0  # Used quota in dollars
    enabled: bool = True
    created_at: Optional[str] = None
    
    def __post_init__(self):
        if self.channel_keys is None:
            self.channel_keys = []
    
    @property
    def remaining_quota(self) -> float:
        """Calculate remaining quota"""
        return max(0.0, self.total_quota - self.used_quota)
    
    @property
    def quota_exceeded(self) -> bool:
        """Check if quota is exceeded"""
        return self.used_quota >= self.total_quota if self.total_quota > 0 else False
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "channel_keys": self.channel_keys,
            "total_quota": self.total_quota,
            "used_quota": self.used_quota,
            "enabled": self.enabled,
            "created_at": self.created_at,
            "remaining_quota": self.remaining_quota,
            "quota_exceeded": self.quota_exceeded
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ApiTokenData':
        return cls(
            name=data.get("name", ""),
            channel_keys=data.get("channel_keys", []),
            total_quota=data.get("total_quota", 0.0),
            used_quota=data.get("used_quota", 0.0),
            enabled=data.get("enabled", True),
            created_at=data.get("created_at")
        )


@dataclass
class ModelPricing:
    """Model pricing configuration"""
    input: float  # Price per 1K input tokens
    output: float  # Price per 1K output tokens
    
    def to_dict(self) -> Dict[str, float]:
        return {
            "input": self.input,
            "output": self.output
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, float]) -> 'ModelPricing':
        return cls(
            input=data.get("input", 0.0),
            output=data.get("output", 0.0)
        )


@dataclass
class DatabaseRecord:
    """Generic database record"""
    key: str
    value: Union[str, Dict[str, Any]]
    usage: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "key": self.key,
            "value": self.value if isinstance(self.value, str) else json.dumps(self.value),
            "usage": self.usage
        }


# Type aliases for better readability
ChannelKey = str
TokenKey = str
ModelName = str
DeploymentName = str