"""
OpenAPI specification for One API
"""

import json
from typing import Dict, Any

def get_openapi_spec() -> Dict[str, Any]:
    """Generate OpenAPI 3.0 specification for One API"""
    
    spec = {
        "openapi": "3.0.3",
        "info": {
            "title": "Awsl One API",
            "version": "1.0.0"
        },
        "servers": [
            {
                "url": "/",
                "description": "Current server"
            }
        ],
        "tags": [
            {
                "name": "OpenAI Proxy",
                "description": "OpenAI API compatible endpoints"
            },
            {
                "name": "Admin API",
                "description": "Administrative endpoints"
            }
        ],
        "components": {
            "securitySchemes": {
                "BearerAuth": {
                    "type": "http",
                    "scheme": "bearer",
                    "description": "API token for accessing OpenAI proxy endpoints"
                },
                "AdminAuth": {
                    "type": "http",
                    "scheme": "bearer",
                    "description": "Admin token for management endpoints"
                }
            },
            "schemas": {
                "ChatCompletionRequest": {
                    "type": "object",
                    "required": ["model", "messages"],
                    "properties": {
                        "model": {
                            "type": "string",
                            "description": "ID of the model to use",
                            "example": "gpt-3.5-turbo"
                        },
                        "messages": {
                            "type": "array",
                            "items": {
                                "$ref": "#/components/schemas/ChatMessage"
                            },
                            "description": "Array of messages comprising the conversation"
                        },
                        "temperature": {
                            "type": "number",
                            "minimum": 0,
                            "maximum": 2,
                            "default": 1,
                            "description": "Sampling temperature"
                        },
                        "max_tokens": {
                            "type": "integer",
                            "minimum": 1,
                            "description": "Maximum number of tokens to generate"
                        },
                        "stream": {
                            "type": "boolean",
                            "default": False,
                            "description": "Whether to stream back partial progress"
                        }
                    }
                },
                "ChatMessage": {
                    "type": "object",
                    "required": ["role", "content"],
                    "properties": {
                        "role": {
                            "type": "string",
                            "enum": ["system", "user", "assistant"],
                            "description": "The role of the message author"
                        },
                        "content": {
                            "type": "string",
                            "description": "The contents of the message"
                        }
                    }
                },
                "ChatCompletionResponse": {
                    "type": "object",
                    "properties": {
                        "id": {
                            "type": "string",
                            "description": "Unique identifier for the chat completion"
                        },
                        "object": {
                            "type": "string",
                            "enum": ["chat.completion"],
                            "description": "Object type"
                        },
                        "created": {
                            "type": "integer",
                            "description": "Unix timestamp"
                        },
                        "model": {
                            "type": "string",
                            "description": "Model used for the completion"
                        },
                        "choices": {
                            "type": "array",
                            "items": {
                                "$ref": "#/components/schemas/ChatChoice"
                            }
                        },
                        "usage": {
                            "$ref": "#/components/schemas/Usage"
                        }
                    }
                },
                "ChatChoice": {
                    "type": "object",
                    "properties": {
                        "index": {
                            "type": "integer",
                            "description": "Choice index"
                        },
                        "message": {
                            "$ref": "#/components/schemas/ChatMessage"
                        },
                        "finish_reason": {
                            "type": "string",
                            "enum": ["stop", "length", "content_filter"],
                            "description": "Reason the model stopped generating tokens"
                        }
                    }
                },
                "Usage": {
                    "type": "object",
                    "properties": {
                        "prompt_tokens": {
                            "type": "integer",
                            "description": "Number of tokens in the prompt"
                        },
                        "completion_tokens": {
                            "type": "integer",
                            "description": "Number of tokens in the completion"
                        },
                        "total_tokens": {
                            "type": "integer",
                            "description": "Total number of tokens used"
                        }
                    }
                },
                "Channel": {
                    "type": "object",
                    "required": ["name", "type", "endpoint", "api_key"],
                    "properties": {
                        "key": {
                            "type": "string",
                            "description": "Unique channel identifier"
                        },
                        "name": {
                            "type": "string",
                            "description": "Channel display name"
                        },
                        "type": {
                            "type": "string",
                            "enum": ["openai", "azure-openai"],
                            "description": "Provider type"
                        },
                        "endpoint": {
                            "type": "string",
                            "format": "uri",
                            "description": "API endpoint URL"
                        },
                        "api_key": {
                            "type": "string",
                            "description": "API key for the provider"
                        },
                        "api_version": {
                            "type": "string",
                            "description": "API version (required for Azure OpenAI)"
                        },
                        "deployment_mapper": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            },
                            "description": "Model name to deployment mapping"
                        },
                        "enabled": {
                            "type": "boolean",
                            "default": True,
                            "description": "Whether the channel is enabled"
                        }
                    }
                },
                "Token": {
                    "type": "object",
                    "required": ["name"],
                    "properties": {
                        "token": {
                            "type": "string",
                            "description": "API token"
                        },
                        "name": {
                            "type": "string",
                            "description": "Token display name"
                        },
                        "channel_keys": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            },
                            "description": "List of channel keys this token can access"
                        },
                        "total_quota": {
                            "type": "number",
                            "minimum": 0,
                            "description": "Total quota in dollars"
                        },
                        "used_quota": {
                            "type": "number",
                            "minimum": 0,
                            "description": "Used quota in dollars"
                        },
                        "remaining_quota": {
                            "type": "number",
                            "minimum": 0,
                            "description": "Remaining quota in dollars"
                        },
                        "enabled": {
                            "type": "boolean",
                            "default": True,
                            "description": "Whether the token is enabled"
                        },
                        "created_at": {
                            "type": "string",
                            "format": "date-time",
                            "description": "Token creation timestamp"
                        }
                    }
                },
                "ErrorResponse": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Error message"
                        }
                    }
                },
                "SuccessResponse": {
                    "type": "object",
                    "properties": {
                        "message": {
                            "type": "string",
                            "description": "Success message"
                        }
                    }
                }
            }
        },
        "paths": {
            "/v1/chat/completions": {
                "post": {
                    "tags": ["OpenAI Proxy"],
                    "summary": "Create chat completion",
                    "description": "Creates a model response for the given chat conversation",
                    "security": [{"BearerAuth": []}],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/ChatCompletionRequest"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/ChatCompletionResponse"
                                    }
                                }
                            }
                        },
                        "400": {
                            "description": "Bad request",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/ErrorResponse"
                                    }
                                }
                            }
                        },
                        "401": {
                            "description": "Unauthorized",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/ErrorResponse"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/admin/db_initialize": {
                "post": {
                    "tags": ["Admin API"],
                    "summary": "Initialize database",
                    "description": "Initialize the database with required tables and default data",
                    "security": [{"AdminAuth": []}],
                    "responses": {
                        "200": {
                            "description": "Database initialized successfully",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/SuccessResponse"
                                    }
                                }
                            }
                        },
                        "401": {
                            "description": "Unauthorized",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/ErrorResponse"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/admin/channel": {
                "get": {
                    "tags": ["Admin API"],
                    "summary": "List channels",
                    "description": "Get all configured channels",
                    "security": [{"AdminAuth": []}],
                    "responses": {
                        "200": {
                            "description": "List of channels",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "channels": {
                                                "type": "array",
                                                "items": {
                                                    "$ref": "#/components/schemas/Channel"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                "post": {
                    "tags": ["Admin API"],
                    "summary": "Create channel",
                    "description": "Create a new channel configuration",
                    "security": [{"AdminAuth": []}],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/Channel"
                                }
                            }
                        }
                    },
                    "responses": {
                        "201": {
                            "description": "Channel created successfully",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "message": {"type": "string"},
                                            "key": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/admin/channel/{key}": {
                "get": {
                    "tags": ["Admin API"],
                    "summary": "Get channel",
                    "description": "Get specific channel configuration",
                    "security": [{"AdminAuth": []}],
                    "parameters": [
                        {
                            "name": "key",
                            "in": "path",
                            "required": True,
                            "schema": {"type": "string"},
                            "description": "Channel key"
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Channel configuration",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "channel": {
                                                "$ref": "#/components/schemas/Channel"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                "put": {
                    "tags": ["Admin API"],
                    "summary": "Update channel",
                    "description": "Update existing channel configuration",
                    "security": [{"AdminAuth": []}],
                    "parameters": [
                        {
                            "name": "key",
                            "in": "path",
                            "required": True,
                            "schema": {"type": "string"},
                            "description": "Channel key"
                        }
                    ],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/Channel"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Channel updated successfully",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/SuccessResponse"
                                    }
                                }
                            }
                        }
                    }
                },
                "delete": {
                    "tags": ["Admin API"],
                    "summary": "Delete channel",
                    "description": "Remove channel configuration",
                    "security": [{"AdminAuth": []}],
                    "parameters": [
                        {
                            "name": "key",
                            "in": "path",
                            "required": True,
                            "schema": {"type": "string"},
                            "description": "Channel key"
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Channel deleted successfully",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/SuccessResponse"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/admin/token": {
                "get": {
                    "tags": ["Admin API"],
                    "summary": "List tokens",
                    "description": "Get all API tokens",
                    "security": [{"AdminAuth": []}],
                    "responses": {
                        "200": {
                            "description": "List of tokens",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "tokens": {
                                                "type": "array",
                                                "items": {
                                                    "$ref": "#/components/schemas/Token"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                "post": {
                    "tags": ["Admin API"],
                    "summary": "Create token",
                    "description": "Create a new API token",
                    "security": [{"AdminAuth": []}],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/Token"
                                }
                            }
                        }
                    },
                    "responses": {
                        "201": {
                            "description": "Token created successfully",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "message": {"type": "string"},
                                            "token": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    return spec


def get_openapi_json() -> str:
    """Get OpenAPI specification as JSON string"""
    return json.dumps(get_openapi_spec(), indent=2)