"""
OpenAPI specification for API documentation
"""


def get_openapi_json():
    """Get OpenAPI JSON specification"""
    return {
        "openapi": "3.0.0",
        "info": {
            "title": "One API",
            "description": "Multi-provider AI API proxy with management capabilities",
            "version": "2.0.0"
        },
        "servers": [
            {
                "url": "/",
                "description": "Current server"
            }
        ],
        "paths": {
            "/v1/chat/completions": {
                "post": {
                    "tags": ["Chat"],
                    "summary": "Create chat completion",
                    "description": "Create a chat completion using various AI models",
                    "security": [{"bearerAuth": []}],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/ChatCompletionRequest"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/ChatCompletionResponse"
                                    }
                                }
                            }
                        },
                        "401": {"description": "Unauthorized"},
                        "429": {"description": "Rate limit exceeded"}
                    }
                }
            },
            "/v1/completions": {
                "post": {
                    "tags": ["Completions"],
                    "summary": "Create text completion",
                    "description": "Create a text completion using various AI models",
                    "security": [{"bearerAuth": []}],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/CompletionRequest"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/CompletionResponse"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/v1/models": {
                "get": {
                    "tags": ["Models"],
                    "summary": "List available models",
                    "description": "List all available models for the authenticated token",
                    "security": [{"bearerAuth": []}],
                    "responses": {
                        "200": {
                            "description": "List of available models",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/ModelsResponse"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/health": {
                "get": {
                    "tags": ["System"],
                    "summary": "Health check",
                    "description": "Check system health status",
                    "responses": {
                        "200": {
                            "description": "System is healthy",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "status": {"type": "string"},
                                            "version": {"type": "string"},
                                            "framework": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        "components": {
            "securitySchemes": {
                "bearerAuth": {
                    "type": "http",
                    "scheme": "bearer",
                    "bearerFormat": "Token"
                }
            },
            "schemas": {
                "ChatCompletionRequest": {
                    "type": "object",
                    "required": ["model", "messages"],
                    "properties": {
                        "model": {"type": "string", "description": "Model identifier"},
                        "messages": {
                            "type": "array",
                            "items": {"$ref": "#/components/schemas/ChatMessage"}
                        },
                        "temperature": {"type": "number", "minimum": 0, "maximum": 2, "default": 1},
                        "max_tokens": {"type": "integer", "minimum": 1},
                        "stream": {"type": "boolean", "default": False}
                    }
                },
                "ChatMessage": {
                    "type": "object",
                    "required": ["role", "content"],
                    "properties": {
                        "role": {"type": "string", "enum": ["system", "user", "assistant"]},
                        "content": {"type": "string"}
                    }
                },
                "ChatCompletionResponse": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "string"},
                        "object": {"type": "string"},
                        "created": {"type": "integer"},
                        "model": {"type": "string"},
                        "choices": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "index": {"type": "integer"},
                                    "message": {"$ref": "#/components/schemas/ChatMessage"},
                                    "finish_reason": {"type": "string"}
                                }
                            }
                        },
                        "usage": {"$ref": "#/components/schemas/Usage"}
                    }
                },
                "CompletionRequest": {
                    "type": "object",
                    "required": ["model", "prompt"],
                    "properties": {
                        "model": {"type": "string"},
                        "prompt": {"type": "string"},
                        "max_tokens": {"type": "integer", "default": 16},
                        "temperature": {"type": "number", "minimum": 0, "maximum": 2, "default": 1}
                    }
                },
                "CompletionResponse": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "string"},
                        "object": {"type": "string"},
                        "created": {"type": "integer"},
                        "model": {"type": "string"},
                        "choices": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "text": {"type": "string"},
                                    "index": {"type": "integer"},
                                    "finish_reason": {"type": "string"}
                                }
                            }
                        },
                        "usage": {"$ref": "#/components/schemas/Usage"}
                    }
                },
                "ModelsResponse": {
                    "type": "object",
                    "properties": {
                        "object": {"type": "string", "default": "list"},
                        "data": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "id": {"type": "string"},
                                    "object": {"type": "string"},
                                    "created": {"type": "integer"},
                                    "owned_by": {"type": "string"}
                                }
                            }
                        }
                    }
                },
                "Usage": {
                    "type": "object",
                    "properties": {
                        "prompt_tokens": {"type": "integer"},
                        "completion_tokens": {"type": "integer"},
                        "total_tokens": {"type": "integer"}
                    }
                }
            }
        }
    }