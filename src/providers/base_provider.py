"""
Base provider class for AI API providers
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from workers import Response


class BaseProvider(ABC):
    """Abstract base class for AI provider implementations"""
    
    @abstractmethod
    async def make_request(self, channel_config: Dict[str, Any], request_data: Dict[str, Any], original_request) -> Response:
        """
        Make a request to the AI provider
        
        Args:
            channel_config: Channel configuration including API key, endpoint, etc.
            request_data: The request payload (JSON)
            original_request: The original request object
            
        Returns:
            Response object from the provider
        """
        pass
    
    @abstractmethod
    def transform_request_data(self, request_data: Dict[str, Any], channel_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform request data for the specific provider
        
        Args:
            request_data: Original request data
            channel_config: Channel configuration
            
        Returns:
            Transformed request data
        """
        pass
    
    @abstractmethod
    def build_request_url(self, channel_config: Dict[str, Any], request_data: Dict[str, Any]) -> str:
        """
        Build the request URL for the provider
        
        Args:
            channel_config: Channel configuration
            request_data: Request data
            
        Returns:
            Complete URL for the API request
        """
        pass
    
    @abstractmethod
    def build_headers(self, channel_config: Dict[str, Any], original_request) -> Dict[str, str]:
        """
        Build headers for the provider request
        
        Args:
            channel_config: Channel configuration
            original_request: Original request object
            
        Returns:
            Headers dictionary
        """
        pass
    
    def get_mapped_model(self, model: str, channel_config: Dict[str, Any]) -> str:
        """
        Get the mapped model name for the provider
        
        Args:
            model: Original model name
            channel_config: Channel configuration with deployment_mapper
            
        Returns:
            Mapped model/deployment name
        """
        deployment_mapper = channel_config.get("deployment_mapper", {})
        return deployment_mapper.get(model, model)
    
    def supports_model(self, model: str, channel_config: Dict[str, Any]) -> bool:
        """
        Check if the channel supports the given model
        
        Args:
            model: Model name to check
            channel_config: Channel configuration
            
        Returns:
            True if model is supported, False otherwise
        """
        deployment_mapper = channel_config.get("deployment_mapper", {})
        return model in deployment_mapper
    
    async def handle_streaming_response(self, response: Response) -> Response:
        """
        Handle streaming response if needed
        
        Args:
            response: Provider response
            
        Returns:
            Processed response
        """
        # Default implementation just returns the response as-is
        # Subclasses can override for custom streaming handling
        return response