"""
Azure OpenAI API provider implementation
"""

from typing import Dict, Any
from workers import Response
from js import fetch
import json
from .base_provider import BaseProvider
from src.utils import create_response, is_stream_request, build_azure_url


class AzureOpenAIProvider(BaseProvider):
    """Azure OpenAI API provider implementation"""
    
    async def make_request(self, channel_config: Dict[str, Any], request_data: Dict[str, Any], original_request) -> Response:
        """Make request to Azure OpenAI API"""
        try:
            # Transform request data
            transformed_data = self.transform_request_data(request_data, channel_config)
            
            # Build request URL
            url = self.build_request_url(channel_config, request_data)
            
            # Build headers
            headers = self.build_headers(channel_config, original_request)
            
            # Make the request
            response = await fetch(url, {
                "method": "POST",
                "headers": headers,
                "body": json.dumps(transformed_data)
            })
            
            # Handle streaming if requested
            if is_stream_request(request_data):
                return await self.handle_streaming_response(response)
            
            return response
            
        except Exception as e:
            print(f"Azure OpenAI provider error: {str(e)}")
            return create_response({"error": "Provider request failed"}, 500)
    
    def transform_request_data(self, request_data: Dict[str, Any], channel_config: Dict[str, Any]) -> Dict[str, Any]:
        """Transform request data for Azure OpenAI API"""
        # Create a copy to avoid modifying the original
        transformed = request_data.copy()
        
        # Map model name to deployment name
        if "model" in transformed:
            deployment_name = self.get_mapped_model(transformed["model"], channel_config)
            # For Azure, we don't include the model in the request body
            # as it's specified in the URL via deployment name
            del transformed["model"]
        
        return transformed
    
    def build_request_url(self, channel_config: Dict[str, Any], request_data: Dict[str, Any]) -> str:
        """Build Azure OpenAI API request URL"""
        endpoint = channel_config["endpoint"]
        api_version = channel_config["api_version"]
        
        # Get the deployment name for the model
        model = request_data.get("model", "")
        deployment_name = self.get_mapped_model(model, channel_config)
        
        if not deployment_name:
            raise ValueError(f"No deployment mapping found for model: {model}")
        
        # Build the Azure-specific URL
        return build_azure_url(endpoint, deployment_name, api_version)
    
    def build_headers(self, channel_config: Dict[str, Any], original_request) -> Dict[str, str]:
        """Build headers for Azure OpenAI API request"""
        headers = {
            "Content-Type": "application/json",
            "api-key": channel_config["api_key"]  # Azure uses api-key instead of Authorization
        }
        
        # Copy relevant headers from original request
        original_headers = original_request.headers
        
        # Copy User-Agent if present
        if "User-Agent" in original_headers:
            headers["User-Agent"] = original_headers["User-Agent"]
        
        # Copy custom Azure OpenAI headers
        azure_headers = [
            "OpenAI-Organization",
            "OpenAI-Project",
            "OpenAI-Beta"
        ]
        
        for header in azure_headers:
            if header in original_headers:
                headers[header] = original_headers[header]
        
        return headers
    
    async def handle_streaming_response(self, response: Response) -> Response:
        """Handle streaming response from Azure OpenAI"""
        # For streaming, we need to return the response as-is
        # The client will handle the Server-Sent Events stream
        return response