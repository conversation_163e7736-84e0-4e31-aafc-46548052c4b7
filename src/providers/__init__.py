"""
AI Provider implementations
"""

from typing import Optional, Dict, Any
from .base_provider import BaseProvider
from .openai_provider import OpenAIProvider
from .azure_openai_provider import AzureOpenAIProvider
from src.constants import PROVIDER_TYPES


def get_provider_handler(provider_type: str) -> Optional[BaseProvider]:
    """Get provider handler by type"""
    if provider_type == PROVIDER_TYPES["OPENAI"]:
        return OpenAIProvider()
    elif provider_type == PROVIDER_TYPES["AZURE_OPENAI"]:
        return AzureOpenAIProvider()
    else:
        return None


__all__ = [
    "BaseProvider",
    "OpenAIProvider", 
    "AzureOpenAIProvider",
    "get_provider_handler"
]