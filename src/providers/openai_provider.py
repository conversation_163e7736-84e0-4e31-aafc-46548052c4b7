"""
OpenAI API provider implementation
"""

from typing import Dict, Any
from workers import Response
from js import fetch
import json
from .base_provider import BaseProvider
from src.utils import create_response, is_stream_request


class OpenAIProvider(BaseProvider):
    """OpenAI API provider implementation"""
    
    async def make_request(self, channel_config: Dict[str, Any], request_data: Dict[str, Any], original_request) -> Response:
        """Make request to OpenAI API"""
        try:
            # Transform request data
            transformed_data = self.transform_request_data(request_data, channel_config)
            
            # Build request URL
            url = self.build_request_url(channel_config, request_data)
            
            # Build headers
            headers = self.build_headers(channel_config, original_request)
            
            # Make the request
            response = await fetch(url, {
                "method": "POST",
                "headers": headers,
                "body": json.dumps(transformed_data)
            })
            
            # Handle streaming if requested
            if is_stream_request(request_data):
                return await self.handle_streaming_response(response)
            
            return response
            
        except Exception as e:
            print(f"OpenAI provider error: {str(e)}")
            return create_response({"error": "Provider request failed"}, 500)
    
    def transform_request_data(self, request_data: Dict[str, Any], channel_config: Dict[str, Any]) -> Dict[str, Any]:
        """Transform request data for OpenAI API"""
        # Create a copy to avoid modifying the original
        transformed = request_data.copy()
        
        # Map model name if needed
        if "model" in transformed:
            transformed["model"] = self.get_mapped_model(transformed["model"], channel_config)
        
        return transformed
    
    def build_request_url(self, channel_config: Dict[str, Any], request_data: Dict[str, Any]) -> str:
        """Build OpenAI API request URL"""
        endpoint = channel_config["endpoint"]
        
        # Ensure endpoint ends with /
        if not endpoint.endswith("/"):
            endpoint += "/"
        
        # Add the chat completions path
        return f"{endpoint}chat/completions"
    
    def build_headers(self, channel_config: Dict[str, Any], original_request) -> Dict[str, str]:
        """Build headers for OpenAI API request"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {channel_config['api_key']}"
        }
        
        # Copy relevant headers from original request
        original_headers = original_request.headers
        
        # Copy User-Agent if present
        if "User-Agent" in original_headers:
            headers["User-Agent"] = original_headers["User-Agent"]
        
        # Copy custom OpenAI headers
        openai_headers = [
            "OpenAI-Organization",
            "OpenAI-Project",
            "OpenAI-Beta"
        ]
        
        for header in openai_headers:
            if header in original_headers:
                headers[header] = original_headers[header]
        
        return headers
    
    async def handle_streaming_response(self, response: Response) -> Response:
        """Handle streaming response from OpenAI"""
        # For streaming, we need to return the response as-is
        # The client will handle the Server-Sent Events stream
        return response