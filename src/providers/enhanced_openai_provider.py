"""
Enhanced OpenAI API provider with improved error handling and retry logic
"""

import asyncio
import json
from typing import Dict, Any, Optional
from workers import Response
from js import fetch
from .base_provider import BaseProvider
from src.utils import create_response, is_stream_request
from src.errors import (
    ProviderError, ErrorCode, RetryableError, handle_errors
)
from src.logging import get_logger, log_provider_request, TimedOperation
from src.database_enhanced import RetryConfig


logger = get_logger(__name__)


class EnhancedOpenAIProvider(BaseProvider):
    """Enhanced OpenAI API provider with robust error handling"""
    
    def __init__(self, retry_config: Optional[RetryConfig] = None):
        self.retry_config = retry_config or RetryConfig(
            max_attempts=3,
            base_delay=1.0,
            max_delay=30.0,
            exponential_base=2.0
        )
        self.logger = logger.with_context(provider="openai")
    
    @handle_errors()
    async def make_request(self, channel_config: Dict[str, Any], request_data: Dict[str, Any], original_request) -> Response:
        """Make request to OpenAI API with retry logic"""
        model = request_data.get("model", "unknown")
        
        async def _make_request():
            log_provider_request(
                self.logger,
                "openai",
                model,
                channel_key=channel_config.get("name", "unknown")
            )
            
            # Transform request data
            transformed_data = self.transform_request_data(request_data, channel_config)
            
            # Build request URL
            url = self.build_request_url(channel_config, request_data)
            
            # Build headers
            headers = self.build_headers(channel_config, original_request)
            
            # Make the request with timeout
            response = await self._fetch_with_timeout(
                url,
                {
                    "method": "POST",
                    "headers": headers,
                    "body": json.dumps(transformed_data)
                },
                timeout=60000  # 60 second timeout
            )
            
            # Check for HTTP errors
            if not self._is_success_status(response.status):
                await self._handle_error_response(response, model)
            
            # Handle streaming if requested
            if is_stream_request(request_data):
                return await self.handle_streaming_response(response)
            
            return response
        
        return await self._retry_request(_make_request, f"openai_request_{model}")
    
    async def _fetch_with_timeout(self, url: str, options: Dict[str, Any], timeout: int = 30000) -> Response:
        """Fetch with timeout handling"""
        try:
            # Note: In Cloudflare Workers Python, timeout is handled at the runtime level
            # We'll implement a basic timeout check
            response = await fetch(url, options)
            return response
        except Exception as e:
            error_msg = str(e).lower()
            if "timeout" in error_msg or "aborted" in error_msg:
                raise ProviderError(
                    message="Request timeout",
                    error_code=ErrorCode.PROVIDER_REQUEST_TIMEOUT,
                    cause=e
                )
            else:
                raise ProviderError(
                    message=f"Network error: {str(e)}",
                    error_code=ErrorCode.PROVIDER_UNAVAILABLE,
                    cause=e
                )
    
    def _is_success_status(self, status: int) -> bool:
        """Check if HTTP status indicates success"""
        return 200 <= status < 300
    
    async def _handle_error_response(self, response: Response, model: str):
        """Handle error responses from OpenAI API"""
        try:
            error_data = await response.json()
            error_info = error_data.get("error", {})
            error_message = error_info.get("message", f"HTTP {response.status}")
            error_type = error_info.get("type", "unknown")
            error_code_str = error_info.get("code", "")
            
        except Exception:
            # Failed to parse error response
            error_message = f"HTTP {response.status}"
            error_type = "unknown"
            error_code_str = ""
        
        # Map OpenAI errors to our error codes
        if response.status == 401:
            raise ProviderError(
                message=f"OpenAI authentication failed: {error_message}",
                error_code=ErrorCode.PROVIDER_AUTHENTICATION_ERROR
            )
        elif response.status == 429:
            # Rate limiting - determine if it's retryable
            retry_after = None
            retry_after_header = response.headers.get("Retry-After")
            if retry_after_header:
                try:
                    retry_after = int(retry_after_header)
                except ValueError:
                    pass
            
            raise RetryableError(
                message=f"OpenAI rate limit exceeded: {error_message}",
                error_code=ErrorCode.PROVIDER_RATE_LIMITED,
                retry_after=retry_after
            )
        elif response.status == 404:
            raise ProviderError(
                message=f"Model not found: {model}",
                error_code=ErrorCode.PROVIDER_MODEL_NOT_FOUND
            )
        elif response.status == 402 or "quota" in error_message.lower():
            raise ProviderError(
                message=f"OpenAI quota exceeded: {error_message}",
                error_code=ErrorCode.PROVIDER_QUOTA_EXCEEDED
            )
        elif response.status >= 500:
            # Server errors are retryable
            raise RetryableError(
                message=f"OpenAI server error: {error_message}",
                error_code=ErrorCode.PROVIDER_UNAVAILABLE,
                http_status=response.status
            )
        else:
            # Client errors are not retryable
            raise ProviderError(
                message=f"OpenAI API error: {error_message}",
                error_code=ErrorCode.PROVIDER_UNAVAILABLE,
                http_status=response.status
            )
    
    async def _retry_request(self, operation, operation_name: str) -> Response:
        """Retry request with exponential backoff"""
        last_exception = None
        
        for attempt in range(self.retry_config.max_attempts):
            try:
                with TimedOperation(self.logger, f"{operation_name} (attempt {attempt + 1})"):
                    return await operation()
                    
            except RetryableError as e:
                last_exception = e
                
                if attempt < self.retry_config.max_attempts - 1:
                    delay = self.retry_config.get_delay(attempt)
                    
                    # Use retry_after from rate limiting if available
                    if hasattr(e, 'retry_after') and e.retry_after:
                        delay = max(delay, e.retry_after)
                    
                    self.logger.warn(
                        f"Provider request failed, retrying in {delay:.2f}s",
                        {
                            "operation": operation_name,
                            "attempt": attempt + 1,
                            "max_attempts": self.retry_config.max_attempts,
                            "error": str(e),
                            "error_code": e.error_code.value
                        }
                    )
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(
                        f"Provider request failed after {self.retry_config.max_attempts} attempts",
                        {"operation": operation_name},
                        error=e
                    )
            
            except ProviderError as e:
                # Non-retryable provider errors
                self.logger.error(f"Provider request failed: {str(e)}", error=e)
                raise
            
            except Exception as e:
                # Unexpected errors
                self.logger.error(f"Unexpected error in provider request: {str(e)}", error=e)
                raise ProviderError(
                    message=f"Unexpected provider error: {str(e)}",
                    error_code=ErrorCode.PROVIDER_UNAVAILABLE,
                    cause=e
                )
        
        # If we get here, all retries failed
        raise last_exception
    
    def transform_request_data(self, request_data: Dict[str, Any], channel_config: Dict[str, Any]) -> Dict[str, Any]:
        """Transform request data for OpenAI API"""
        # Create a copy to avoid modifying the original
        transformed = request_data.copy()
        
        # Map model name if needed
        if "model" in transformed:
            original_model = transformed["model"]
            mapped_model = self.get_mapped_model(original_model, channel_config)
            transformed["model"] = mapped_model
            
            self.logger.debug(
                f"Model mapping: {original_model} -> {mapped_model}",
                {"original_model": original_model, "mapped_model": mapped_model}
            )
        
        # Validate required fields
        if "messages" not in transformed:
            raise ProviderError(
                message="Missing required field: messages",
                error_code=ErrorCode.INVALID_REQUEST_FORMAT
            )
        
        # Validate message format
        messages = transformed.get("messages", [])
        if not isinstance(messages, list) or len(messages) == 0:
            raise ProviderError(
                message="Messages must be a non-empty array",
                error_code=ErrorCode.INVALID_REQUEST_FORMAT
            )
        
        for i, message in enumerate(messages):
            if not isinstance(message, dict) or "role" not in message or "content" not in message:
                raise ProviderError(
                    message=f"Invalid message format at index {i}",
                    error_code=ErrorCode.INVALID_REQUEST_FORMAT
                )
        
        return transformed
    
    def build_request_url(self, channel_config: Dict[str, Any], request_data: Dict[str, Any]) -> str:
        """Build OpenAI API request URL"""
        endpoint = channel_config.get("endpoint")
        if not endpoint:
            raise ProviderError(
                message="Missing endpoint in channel configuration",
                error_code=ErrorCode.CHANNEL_CONFIGURATION_ERROR
            )
        
        # Ensure endpoint ends with /
        if not endpoint.endswith("/"):
            endpoint += "/"
        
        # Add the chat completions path
        return f"{endpoint}chat/completions"
    
    def build_headers(self, channel_config: Dict[str, Any], original_request) -> Dict[str, str]:
        """Build headers for OpenAI API request"""
        api_key = channel_config.get("api_key")
        if not api_key:
            raise ProviderError(
                message="Missing API key in channel configuration",
                error_code=ErrorCode.CHANNEL_CONFIGURATION_ERROR
            )
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        # Copy relevant headers from original request
        try:
            original_headers = original_request.headers
            
            # Copy User-Agent if present
            user_agent = original_headers.get("User-Agent")
            if user_agent:
                headers["User-Agent"] = user_agent
            
            # Copy custom OpenAI headers
            openai_headers = [
                "OpenAI-Organization",
                "OpenAI-Project",
                "OpenAI-Beta"
            ]
            
            for header in openai_headers:
                value = original_headers.get(header)
                if value:
                    headers[header] = value
                    
        except Exception as e:
            self.logger.warn(f"Failed to copy headers from original request: {str(e)}")
        
        return headers
    
    async def handle_streaming_response(self, response: Response) -> Response:
        """Handle streaming response from OpenAI"""
        try:
            # For streaming, we need to return the response as-is
            # The client will handle the Server-Sent Events stream
            self.logger.debug("Handling streaming response")
            return response
        except Exception as e:
            self.logger.error("Error handling streaming response", error=e)
            raise ProviderError(
                message=f"Failed to handle streaming response: {str(e)}",
                error_code=ErrorCode.PROVIDER_UNAVAILABLE,
                cause=e
            )