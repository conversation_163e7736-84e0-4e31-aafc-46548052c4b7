"""
Simplified database initialization for debugging
"""

import json
from typing import Dict, Any, Optional


class SimpleDatabaseManager:
    """Simplified database manager for debugging"""
    
    def __init__(self, db_binding):
        self.db = db_binding
    
    async def initialize(self) -> bool:
        """Simple database initialization"""
        try:
            print("Creating channel_config table...")
            await self.db.prepare("""
                CREATE TABLE IF NOT EXISTS channel_config (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL
                )
            """).run()
            
            print("Creating api_token table...")
            await self.db.prepare("""
                CREATE TABLE IF NOT EXISTS api_token (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    usage REAL DEFAULT 0.0
                )
            """).run()
            
            print("Creating settings table...")
            await self.db.prepare("""
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL
                )
            """).run()
            
            print("Database tables created successfully")
            return True
            
        except Exception as e:
            print(f"Database initialization error: {str(e)}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            return False
    
    async def test_connection(self) -> bool:
        """Test database connection"""
        try:
            result = await self.db.prepare("SELECT 1 as test").first()
            print(f"Database connection test result: {result}")
            return True
        except Exception as e:
            print(f"Database connection test failed: {str(e)}")
            return False