#!/usr/bin/env python3
"""
频道加载问题诊断脚本
"""

import requests
import json
import sys

# 配置
DOMAIN = "https://deepestcode.com"
ADMIN_TOKEN = "thisismytoken"

def check_api_endpoint(url, headers=None, method="GET", data=None, description=""):
    """检查API端点并显示详细信息"""
    print(f"\n🔍 {description}")
    print(f"   请求: {method} {url}")
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers, timeout=10)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data, timeout=10)
        
        print(f"   状态码: {response.status_code}")
        
        # 显示响应头
        content_type = response.headers.get('content-type', 'unknown')
        print(f"   Content-Type: {content_type}")
        
        # 解析和显示响应
        if content_type.startswith('application/json'):
            try:
                json_data = response.json()
                print(f"   响应内容:")
                print(f"   {json.dumps(json_data, indent=4, ensure_ascii=False)}")
                return response.status_code == 200, json_data
            except json.JSONDecodeError as e:
                print(f"   JSON解析错误: {e}")
                print(f"   原始响应: {response.text[:500]}...")
                return False, None
        else:
            text_content = response.text[:200]
            print(f"   响应内容: {text_content}...")
            return response.status_code == 200, response.text
    
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 请求失败: {str(e)}")
        return False, None

def main():
    print("🔧 One API 频道诊断工具")
    print("=" * 60)
    
    headers = {
        "Authorization": f"Bearer {ADMIN_TOKEN}",
        "Content-Type": "application/json",
        "User-Agent": "OneAPI-Diagnostic/1.0"
    }
    
    # 1. 检查基本连接
    success, _ = check_api_endpoint(
        f"{DOMAIN}/", 
        description="检查服务基本连接"
    )
    
    if not success:
        print("\n❌ 基本连接失败，请检查域名和服务状态")
        return
    
    # 2. 检查管理员API认证
    success, _ = check_api_endpoint(
        f"{DOMAIN}/api/admin/", 
        headers=headers,
        description="验证管理员认证"
    )
    
    if not success:
        print("\n❌ 管理员认证失败，请检查ADMIN_TOKEN")
        return
    
    # 3. 检查数据库状态
    print("\n📊 检查数据库初始化状态...")
    success, response = check_api_endpoint(
        f"{DOMAIN}/api/admin/db_initialize", 
        headers=headers,
        method="POST",
        description="尝试初始化数据库"
    )
    
    # 4. 获取频道列表
    print("\n📋 获取现有频道列表...")
    success, channels_data = check_api_endpoint(
        f"{DOMAIN}/api/admin/channel", 
        headers=headers,
        description="获取所有频道"
    )
    
    if success and isinstance(channels_data, list):
        if len(channels_data) == 0:
            print("\n⚠️  数据库中没有任何频道配置！")
            print("\n🔧 解决方案：创建测试频道...")
            
            # 创建OpenAI测试频道
            test_channel = {
                "name": "OpenAI测试频道",
                "type": "openai",
                "endpoint": "https://api.openai.com/v1/",
                "api_key": "sk-your-openai-key-here",  # 需要用户替换
                "deployment_mapper": {
                    "gpt-4": "gpt-4",
                    "gpt-3.5-turbo": "gpt-3.5-turbo",
                    "gpt-4o": "gpt-4o"
                },
                "enabled": True
            }
            
            print(f"\n创建测试频道数据:")
            print(json.dumps(test_channel, indent=2, ensure_ascii=False))
            
            success, create_response = check_api_endpoint(
                f"{DOMAIN}/api/admin/channel", 
                headers=headers,
                method="POST",
                data=test_channel,
                description="创建OpenAI测试频道"
            )
            
            if success:
                print("\n✅ 测试频道创建成功！")
                
                # 重新获取频道列表验证
                print("\n🔄 重新获取频道列表验证...")
                check_api_endpoint(
                    f"{DOMAIN}/api/admin/channel", 
                    headers=headers,
                    description="验证频道创建结果"
                )
            else:
                print("\n❌ 测试频道创建失败")
        else:
            print(f"\n✅ 找到 {len(channels_data)} 个频道配置")
            for i, channel in enumerate(channels_data):
                try:
                    channel_info = json.loads(channel.get('value', '{}'))
                    print(f"   {i+1}. {channel_info.get('name', 'Unknown')} ({channel_info.get('type', 'Unknown')})")
                except:
                    print(f"   {i+1}. 频道数据解析失败")
    
    # 5. 检查健康状态（如果有的话）
    print("\n🏥 检查系统健康状态...")
    check_api_endpoint(
        f"{DOMAIN}/health", 
        description="获取系统健康状态"
    )
    
    # 6. 提供手动创建频道的命令
    print("\n" + "=" * 60)
    print("📖 手动创建频道命令:")
    print("\n1. OpenAI频道:")
    print(f"""curl -X POST {DOMAIN}/api/admin/channel \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer {ADMIN_TOKEN}" \\
  -d '{{
    "name": "OpenAI主渠道",
    "type": "openai",
    "endpoint": "https://api.openai.com/v1/",
    "api_key": "你的OpenAI-API-Key",
    "deployment_mapper": {{
      "gpt-4": "gpt-4",
      "gpt-3.5-turbo": "gpt-3.5-turbo"
    }}
  }}'""")
    
    print("\n2. Azure OpenAI频道:")
    print(f"""curl -X POST {DOMAIN}/api/admin/channel \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer {ADMIN_TOKEN}" \\
  -d '{{
    "name": "Azure OpenAI",
    "type": "azure-openai",
    "endpoint": "https://your-resource.openai.azure.com/",
    "api_key": "你的Azure-API-Key",
    "api_version": "2024-02-15-preview",
    "deployment_mapper": {{
      "gpt-4": "gpt-4-deployment-name"
    }}
  }}'""")

if __name__ == "__main__":
    main()