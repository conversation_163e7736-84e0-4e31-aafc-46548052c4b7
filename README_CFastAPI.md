# One API - CFastAPI Version

基于 **CFastAPI** 的多提供商 AI API 代理服务，专为 **Cloudflare Workers** 设计，保持原有部署方式的同时提供 FastAPI 风格的开发体验。

## 🚀 特性

- **FastAPI 风格**: 使用装饰器、依赖注入、Pydantic 验证
- **Cloudflare Workers 兼容**: 完全运行在 Cloudflare Workers 上
- **多提供商支持**: OpenAI、Azure OpenAI 等
- **原有功能保持**: 渠道管理、Token 管理、使用统计
- **类型安全**: 使用 Pydantic 进行请求/响应验证
- **依赖注入**: 清晰的依赖管理和测试友好

## 📋 项目结构

```
src/
├── cfastapi/           # CFastAPI 框架核心
│   ├── app.py         # 主应用类
│   ├── routing.py     # 路由系统
│   ├── depends.py     # 依赖注入
│   ├── middleware.py  # 中间件
│   └── exceptions.py  # 异常处理
├── api/               # API 路由
│   ├── v1/           # v1 API 端点
│   └── admin/        # 管理 API
├── schemas/          # Pydantic 数据模型
├── services/         # 依赖注入服务
└── models.py         # 原有数据模型

main_cfastapi.py      # CFastAPI 入口文件
main.py              # 原始入口文件
wrangler.toml        # Cloudflare 配置
```

## 🔧 使用方法

### 1. 切换到 CFastAPI 版本

编辑 `wrangler.toml`：
```toml
main = "main_cfastapi.py"  # 改为使用 CFastAPI 版本
```

### 2. 部署到 Cloudflare Workers

```bash
# 保持原有部署方式
npx wrangler deploy
```

### 3. API 使用保持不变

```bash
# Chat completion
curl -X POST "https://your-domain.com/v1/chat/completions" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{"model": "gpt-3.5-turbo", "messages": [{"role": "user", "content": "Hello"}]}'

# 管理 API
curl -X GET "https://your-domain.com/api/admin/channels" \
  -H "Authorization: Bearer your-admin-token"
```

## 🏗️ CFastAPI 框架特性

### 路由装饰器
```python
from src.cfastapi import CFastAPI
from src.cfastapi.routing import Router

router = Router()

@router.post("/completions")
async def create_completion(
    request_data: ChatCompletionRequest,
    token: ApiTokenData = Depends(get_current_token)
):
    return {"response": "data"}
```

### 依赖注入
```python
from src.cfastapi import Depends

async def get_database(app) -> DatabaseManager:
    env = app.state.get('env')
    return DatabaseManager(env.DB)

@router.get("/data")
async def get_data(db: DatabaseManager = Depends(get_database)):
    return await db.get_all_channels()
```

### Pydantic 验证
```python
from pydantic import BaseModel

class ChatRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: Optional[float] = 1.0

@router.post("/chat")
async def chat(request: ChatRequest):
    # 自动验证请求数据
    return process_chat(request)
```

### 中间件支持
```python
from src.cfastapi import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"]
)
```

## 🔄 从原版本迁移

### 代码对比

**原版本 (手动路由)**:
```python
class OneAPIApp:
    async def handle_request(self, request):
        url = URL.new(request.url)
        if url.pathname.startswith('/v1/chat'):
            return await self._handle_chat_completion(request)
        # ...更多手动路由
```

**CFastAPI 版本**:
```python
@router.post("/chat/completions", response_model=ChatCompletionResponse)
async def create_chat_completion(
    request_data: ChatCompletionRequest,
    token: ApiTokenData = Depends(get_current_token),
    db: DatabaseManager = Depends(get_database)
):
    # 自动参数解析、验证、依赖注入
    return process_completion(request_data, token, db)
```

### 迁移优势

1. **代码更清晰**: 声明式路由替代手动路由匹配
2. **类型安全**: Pydantic 自动验证和类型检查
3. **依赖注入**: 更好的测试和模块化
4. **自动文档**: 基于类型提示生成 API 文档
5. **错误处理**: 统一的异常处理和响应格式

## 📚 API 文档

- **Swagger UI**: `https://your-domain.com/api/docs`
- **ReDoc**: `https://your-domain.com/api/redocs`
- **OpenAPI JSON**: `https://your-domain.com/api/openapi.json`

## 🔧 开发指南

### 添加新端点
```python
# 1. 创建 Pydantic 模型
class NewRequest(BaseModel):
    param1: str
    param2: Optional[int] = None

# 2. 添加路由
@router.post("/new-endpoint")
async def new_endpoint(
    request: NewRequest,
    token: ApiTokenData = Depends(get_current_token)
):
    return {"result": "success"}
```

### 自定义依赖
```python
async def get_specific_service(app) -> MyService:
    return MyService(app.state.get('env'))

@router.get("/service-data")
async def get_service_data(
    service: MyService = Depends(get_specific_service)
):
    return await service.get_data()
```

## 🚀 部署说明

### 环境要求
- Cloudflare Workers Python 运行时
- Cloudflare D1 数据库
- Cloudflare Assets (可选，用于管理界面)

### 配置文件
保持原有的 `wrangler.toml` 配置不变，只需修改入口文件：
```toml
main = "main_cfastapi.py"
```

### 环境变量
```toml
[vars]
ADMIN_TOKEN = "your-admin-token"
```

## ⚡ 性能和兼容性

- **性能**: 与原版本相同，在 Cloudflare Workers 上运行
- **内存**: 轻量级框架，专为 Workers 环境优化
- **兼容性**: 100% 兼容原有 API 接口
- **部署**: 保持原有部署方式和配置

## 🔍 调试和日志

CFastAPI 提供了更好的错误信息和调试支持：

```python
# 自动的请求/响应日志
# 详细的错误堆栈跟踪
# 类型验证错误信息
```

## 🎯 总结

CFastAPI 版本提供了现代 Python Web 开发的最佳实践，同时完全兼容 Cloudflare Workers 环境。您可以享受 FastAPI 风格的开发体验，而无需改变部署方式或 API 接口。