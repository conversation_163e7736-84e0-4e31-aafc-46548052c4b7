# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 Cloudflare Workers Python 的 OpenAI API 代理服务，支持多渠道管理、Token 管理和使用量统计。该项目将 OpenAI 和 Azure OpenAI 等多个 AI 服务统一到一个 API 接口下。

## 常用命令

### 开发
```bash
pnpm dev                    # 本地开发服务器
pnpm run deploy            # 部署到生产环境
pnpm run db:create         # 创建 Cloudflare D1 数据库
pnpm run db:init           # 初始化数据库（需要先部署）
```

### 测试和验证
项目没有传统的测试框架，主要通过：
- Web 管理界面测试（访问部署的域名）
- API 测试工具（内置在 Web 界面中）
- 直接 API 调用验证

## 核心架构

### 请求流程
1. **入口点**: `main.py` 中的 `on_fetch()` 函数
2. **路由分发**: `OneAPIApp.handle_request()` 处理不同类型的请求
3. **API 代理**: `/v1/` 路径转发到 AI 提供商
4. **管理接口**: `/api/admin/` 路径处理配置管理

### 关键模块

#### 数据库层 (`src/database.py`)
- 使用 Cloudflare D1 数据库
- 主要表：`channel_config`、`api_token`、`settings`
- 支持渠道配置、Token 管理、设置存储

#### 提供商系统 (`src/providers/`)
- **基类**: `BaseProvider` 定义统一接口
- **OpenAI**: `OpenAIProvider` 处理 OpenAI API
- **Azure OpenAI**: `AzureOpenAIProvider` 处理 Azure OpenAI API
- 支持模型映射和自定义定价

#### 模型定义 (`src/models.py`)
- `ChannelConfig`: 渠道配置
- `ApiTokenData`: API Token 数据
- `Usage`: Token 使用量统计
- `ModelPricing`: 模型定价配置

#### 管理 API (`src/admin/admin_api.py`)
- 渠道管理：创建、更新、删除渠道
- Token 管理：创建、更新、删除 API Token
- 定价管理：全局模型定价配置
- 数据库初始化

### 部署环境配置

#### Cloudflare Workers 配置 (`wrangler.toml`)
- Python Workers 运行时
- D1 数据库绑定
- 静态资源绑定（Web 管理界面）
- 环境变量配置

#### 关键环境变量
- `ADMIN_TOKEN`: 管理员访问令牌
- `DB`: D1 数据库绑定
- `ASSETS`: 静态资源绑定

## 代码约定

### Python 代码风格
- 使用 dataclass 定义数据模型
- 异步编程模式（async/await）
- 类型提示（typing 模块）
- 错误处理和日志记录

### 数据库操作
- 所有数据库操作都通过 `DatabaseManager` 类
- JSON 序列化存储复杂数据结构
- 支持 Cloudflare Workers Python 环境的 JsProxy 对象处理

### API 设计
- RESTful API 设计
- 统一的错误响应格式
- Bearer Token 认证
- CORS 支持

## 新增功能开发指南

### 添加新的 AI 提供商
1. 在 `src/providers/` 创建新的提供商类，继承 `BaseProvider`
2. 实现必需的抽象方法：`make_request`, `transform_request_data`, `build_request_url`, `build_headers`
3. 在 `src/providers/__init__.py` 注册新提供商
4. 更新管理界面的渠道类型选项

### 扩展数据模型
1. 在 `src/models.py` 定义新的数据类
2. 实现 `to_dict()` 和 `from_dict()` 方法
3. 如需数据库存储，在 `DatabaseManager` 添加相应操作方法

### 添加管理 API 端点
1. 在 `src/admin/admin_api.py` 的 `AdminAPI` 类添加新方法
2. 在 `handle_request()` 中添加路由映射
3. 确保适当的权限验证（ADMIN_TOKEN）

## 错误处理和监控

### 增强的错误处理系统
项目实现了全面的错误处理和监控系统：

#### 错误分类 (`src/errors.py`)
- **认证错误** (1000-1099): 无效Token、配额超限等
- **验证错误** (1100-1199): 请求格式错误、缺少字段等  
- **数据库错误** (1200-1299): 连接错误、查询超时等
- **提供商错误** (1300-1399): API不可用、限流、认证失败等
- **渠道错误** (1400-1499): 渠道不存在、配置错误等
- **内部错误** (1500-1599): 服务器错误、超时等

#### 结构化日志系统 (`src/logging.py`)
- 支持不同日志级别：DEBUG、INFO、WARN、ERROR、FATAL
- 结构化日志输出（JSON格式）
- 上下文信息追踪：请求ID、用户、模型、渠道等
- 性能计时和操作追踪

#### 性能监控 (`src/monitoring.py`)
- 实时请求追踪和性能统计
- 错误率和响应时间监控
- 健康检查和系统状态监控
- 自动清理历史数据

### 使用增强功能

#### 启用增强版本
要使用增强的错误处理和监控，需要：
1. 使用 `main_enhanced.py` 替代 `main.py`
2. 在 `wrangler.toml` 中更新入口文件：`main = "main_enhanced.py"`

#### 监控端点
- `/health` - 系统健康状态检查
- `/metrics` - 性能指标和统计信息

#### 数据库重试机制
- 自动重试失败的数据库操作
- 指数退避延迟策略
- 详细的错误分类和处理

#### 提供商请求优化
- 智能重试逻辑（区分可重试和不可重试错误）
- 超时处理和网络错误恢复
- 详细的错误映射和状态码处理

### 调试和故障排除

#### 日志级别控制
- 开发环境自动启用DEBUG级别日志
- 生产环境使用INFO级别，减少日志量
- 通过环境变量 `ADMIN_TOKEN` 检测开发模式

#### 错误响应格式
```json
{
  "error": {
    "message": "错误描述",
    "code": "1001", 
    "type": "AuthenticationError",
    "details": [...]
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 性能监控数据
- 请求数量、成功率、平均响应时间
- 错误分布和趋势分析  
- Token使用量和成本统计
- 活跃请求和历史记录

### 常见问题解决

#### Cloudflare Workers Python 特殊处理
- JsProxy 对象访问：优先使用属性访问，降级到字典访问
- 数据库结果处理：需要特殊处理 D1 返回的结果格式
- 环境变量访问：兼容不同的环境对象格式

#### 调试技巧
- 使用结构化日志系统替代简单的 `print()`
- 通过 `/health` 和 `/metrics` 端点监控系统状态
- 检查日志中的请求ID进行链路追踪
- 在开发环境中启用详细的错误堆栈跟踪

#### 错误处理最佳实践
- 使用自定义异常类型而非通用Exception
- 为所有外部调用添加超时和重试机制
- 记录足够的上下文信息用于问题诊断
- 区分可重试和不可重试的错误类型