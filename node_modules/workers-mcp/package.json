{"name": "workers-mcp", "version": "0.0.13", "description": "MCP transport to your own Cloudflare Worker", "license": "Apache-2.0", "author": "Cloudflare, Inc. (https://cloudflare.com)", "homepage": "https://github.com/cloudflare/workers-mcp", "bugs": "https://github.com/cloudflare/workers-mcp/issues", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "README.md"], "access": "public", "bin": {"workers-mcp": "dist/cli.js"}, "scripts": {"build": "tsup src/*.ts --dts --format esm", "postbuild": "shx chmod +x dist/*.js", "check": "tsc --noEmit", "build:watch": "pnpm build --watch"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241205.0", "@types/fs-extra": "^11.0.4", "@types/node": "^22.10.2", "@types/npm-which": "^3.0.3", "prettier": "^3.4.2", "shx": "^0.3.4", "tsup": "^8.3.5", "typescript": "^5.7.2"}, "engines": {"node": ">=16.17.0"}, "dependencies": {"@clack/prompts": "^0.8.2", "@modelcontextprotocol/sdk": "^1.0.3", "@silvia-odwyer/photon-node": "^0.3.3", "chalk": "^5.3.0", "fs-extra": "^11.2.0", "jsdoc-api": "^9.3.4", "just-filter-object": "^3.2.0", "just-map-values": "^3.2.0", "npm-which": "^3.0.1", "tmp-promise": "^3.0.3", "ts-blank-space": "^0.4.4", "tsx": "^4.19.2"}}