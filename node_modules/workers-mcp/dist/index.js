// src/modules/Proxy.ts
async function Proxy(request, secret, sendRPC) {
  const { pathname } = new URL(request.url);
  const authorization = request.headers.get("Authorization")?.replace(/^Bearer /, "") || "";
  if (authorization !== secret || secret.length !== 64) {
    return new Response("Unauthorized", { status: 401 });
  }
  if (pathname === "/rpc" && request.method === "POST") {
    const { method, args = [] } = await request.json();
    try {
      const result = await sendRPC(method, args);
      if (result instanceof Response) {
        return result;
      } else if (typeof result === "string") {
        return new Response(result);
      } else {
        return Response.json(result);
      }
    } catch (e) {
      return Response.json({
        content: [
          { type: "text", text: e.message },
          { type: "text", text: JSON.stringify(e.stack) }
        ],
        isError: true
      });
    }
  }
  return new Response(null, { status: 404 });
}

// src/modules/ProxyToSelf.ts
var ProxyToSelf = class {
  constructor(worker) {
    this.worker = worker;
    this.env = worker.env;
  }
  env;
  async fetch(request) {
    return Proxy(request, this.env.SHARED_SECRET, (method, args) => {
      const methodReference = this.worker[method];
      if (!methodReference) {
        throw new Error(`WorkerEntrypoint ${this.worker.constructor.name} has no method '${method}'`);
      }
      return this.worker[method].call(this.worker, ...args);
    });
  }
};
export {
  ProxyToSelf
};
