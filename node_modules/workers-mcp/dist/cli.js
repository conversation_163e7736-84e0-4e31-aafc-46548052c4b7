#!/usr/bin/env node

// src/scripts/generate-docs.ts
import fs from "fs-extra";
import tsBlankSpace from "ts-blank-space";
import jsdoc from "jsdoc-api";
import path from "node:path";
import chalk from "chalk";
import filter from "just-filter-object";
async function generateDocs(filename) {
  if (!filename) throw new Error(`Missing filename`);
  const source = tsBlankSpace(fs.readFileSync(filename, "utf8"));
  const data = (await jsdoc.explain({ source, cache: true })).filter((point) => !point.ignore);
  const exported_classes = {};
  let default_export;
  for (const point of data) {
    if (point.kind === "class" && point.meta?.code?.type === "ClassDeclaration") {
      let exported_as = void 0;
      let name = point.meta.code.name;
      if (name.startsWith("exports.")) {
        name = name.slice("exports.".length);
        exported_as = name;
      } else if (name === "module.exports") {
        const raw = source.substring(...point.meta.range);
        const match = raw.match(/class (\w+) (extends|{)/);
        name = match ? match[1] : "default";
        exported_as = "default";
        default_export = { class_name: name, range: point.meta.range };
      }
      const proxy = point.tags?.find(({ title }) => title.startsWith(`do-proxy-`));
      exported_classes[name] = Object.assign(
        exported_classes[name] || {},
        filter(
          {
            exported_as,
            description: point.classdesc || null,
            methods: [],
            statics: {},
            proxy: proxy ? { entrypoint: proxy.value, strategy: proxy.title.slice("do-proxy-".length) } : void 0
          },
          (_, v) => v !== void 0
        )
      );
    }
  }
  for (const point of data) {
    const memberof = point.memberof === "module.exports" || !point.memberof && default_export && rangeWithin(point.meta?.range, default_export.range) ? default_export?.class_name : point.memberof;
    if (point.kind === "function" && point.meta?.code?.type === "MethodDefinition" && memberof) {
      const ex = exported_classes[memberof];
      if (!ex) {
        throw new Error(
          `Missing memberof ${memberof}. Got ${JSON.stringify(point)}, had ${JSON.stringify(Object.keys(exported_classes))}`
        );
      }
      if (point.access === "private") continue;
      let returns = null;
      if (point.returns) {
        const [ret, ...rest] = point.returns;
        if (!ret || rest.length > 0 || ret.type?.names.length !== 1) {
          console.log(`WARN: unexpected returns value for ${JSON.stringify(point)}`);
        }
        returns = { description: ret.description, type: ret.type.names[0] };
      }
      const params = (point.params || []).map(({ description, type, name, optional }) => {
        if (type.names.length !== 1) {
          console.log(`WARN: unexpected params value for ${JSON.stringify(point)}`);
          return null;
        }
        return { description, name, type: type.names[0], optional };
      }).filter((p) => p !== null);
      ex.methods.push({
        name: point.name,
        description: point.description,
        params,
        returns,
        ...point.examples ? { examples: point.examples } : {}
      });
    }
    if (point.kind === "member" && point.meta?.code?.type === "ClassProperty" && memberof && point.meta?.range && source.substring(...point.meta.range).match(/^\s*static\s/)) {
      const ex = exported_classes[memberof];
      if (!ex) {
        throw new Error(
          `Missing memberof ${memberof}. Got ${JSON.stringify(point)}, had ${JSON.stringify(Object.keys(exported_classes))}`
        );
      }
      const members = [];
      for (const subpoint of data) {
        if (subpoint.meta?.code?.id !== point.meta?.code?.id && rangeWithin(subpoint.meta?.range, point.meta.range)) {
          const type = subpoint.meta?.code.type === "Literal" ? "string" : subpoint.returns?.[0]?.type?.names?.[0];
          members.push({
            name: subpoint.name,
            description: subpoint.description,
            type
          });
        }
      }
      ex.statics[point.name] = members;
    }
  }
  for (const point of data) {
    if (point.kind === "member" && point.scope === "global" && point.meta?.code?.name?.startsWith("exports.")) {
      let name = point.name;
      const renamed = source.substring(...point.meta.range).match(/(\w+) as \w+/);
      if (renamed) {
        name = renamed[1];
      }
      if (exported_classes[name]) {
        exported_classes[name].exported_as = point.name;
      } else {
        console.log(`WARN: couldn't find which class to export for ${JSON.stringify(point)}`);
      }
    }
  }
  for (const [name, cls] of Object.entries(exported_classes)) {
    if (cls.proxy) {
      const target = exported_classes[cls.proxy.entrypoint];
      if (!target)
        console.log(`WARN: couldn't find which class to proxy for ${name}. Looking for ${cls.proxy.entrypoint}`);
      for (const method of target.methods) {
        cls.methods.push({
          ...method,
          ...cls.proxy.strategy === "prepend-session-id" ? {
            params: [
              {
                type: "string",
                name: "sessionID",
                description: 'A unique identifier to be used for all tool calls during this conversation. Unless the user specifies explicitly which "session identifier" to use, the system should generate a completely random string of at least 8 characters.'
              },
              ...method.params
            ]
          } : {}
        });
      }
      delete cls.proxy;
    }
  }
  await fs.ensureDir("dist");
  await fs.writeFile(path.join("dist", "docs.json"), JSON.stringify(exported_classes, null, 2));
  console.log(`Generated docs for ${chalk.green(filename)} in ${chalk.yellow("dist/docs.json")}`);
  console.log(
    chalk.gray(
      Object.entries(exported_classes).flatMap(([k, v]) => [
        `\u2022 ${chalk.green(k)} exported as ${chalk.green(v.exported_as)}`,
        ...v.methods.map(
          (m) => `  - ${chalk.yellow(m.name)}(${m.params.map((p) => `${chalk.white(p.name)}: ${p.type || "?"}`).join(", ")}): ${m.returns?.type || "?"}`
        )
      ]).join("\n")
    )
  );
}
function rangeWithin(inner, outer) {
  if (!inner) return false;
  const [a, b] = inner;
  const [x, y] = outer;
  return a >= x && b <= y;
}

// src/scripts/secret.ts
import fs2 from "node:fs";
import crypto from "node:crypto";
import chalk2 from "chalk";

// src/scripts/utils.ts
import { spawn } from "child_process";
async function runCommand(command, args2) {
  const child = spawn(command, args2, {
    stdio: ["inherit", "pipe", "inherit"]
    // Pipe stdout but keep stdin and stderr as inherit
  });
  let output = "";
  child.stdout.on("data", (data) => {
    output += data.toString();
    process.stdout.write(data);
  });
  await new Promise((resolve, reject) => {
    child.on("exit", (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Process exited with code ${code}`));
      }
    });
    child.on("error", reject);
  });
  return output;
}
async function runWithStdin(command, args2, stdin) {
  const child = spawn(command, args2, {
    stdio: ["pipe", "inherit", "inherit"]
  });
  child.stdin.write(stdin + "\n");
  child.stdin.end();
  await new Promise((resolve, reject) => {
    child.on("exit", (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Process exited with code ${code}`));
      }
    });
    child.on("error", reject);
  });
}
var EXAMPLE_TS = `
import { WorkerEntrypoint } from 'cloudflare:workers'
import { ProxyToSelf } from 'workers-mcp'

export default class MyWorker extends WorkerEntrypoint<Env> {
  /**
   * A warm, friendly greeting from your new Workers MCP server.
   * @param name {string} the name of the person we are greeting.
   * @return {string} the contents of our greeting.
   */
  sayHello(name: string) {
    return \`Hello from an MCP Worker, \${name}!\`
  }

  /**
   * @ignore
   **/
  async fetch(request: Request): Promise<Response> {
    return new ProxyToSelf(this).fetch(request)
  }
}
`;
var EXAMPLE_JS = `
import { WorkerEntrypoint } from 'cloudflare:workers'
import { ProxyToSelf } from 'workers-mcp'

export default class MyWorker extends WorkerEntrypoint {
  /**
   * A warm, friendly greeting from your new Workers MCP server.
   * @param name {string} the name of the person we are greeting.
   * @return {string} the contents of our greeting.
   */
  sayHello(name) {
    return \`Hello from an MCP Worker, \${name}!\`
  }

  /**
   * @ignore
   **/
  async fetch(request) {
    return new ProxyToSelf(this).fetch(request)
  }
}
`;

// src/scripts/secret.ts
var SECRET_PATH = ".dev.vars";
function readSharedSecret() {
  return fs2.readFileSync(SECRET_PATH, "utf8").split("\n").map((line) => {
    const match = line.match(/SHARED_SECRET=(.*)/);
    return match?.[1];
  }).find(Boolean);
}
function generateSecret() {
  const random_bytes = crypto.randomBytes(32);
  const random_string = random_bytes.toString("hex");
  const dev_vars = [`SHARED_SECRET=${random_string}`];
  if (fs2.existsSync(SECRET_PATH)) {
    fs2.readFileSync(SECRET_PATH, "utf8").split("\n").forEach((line) => {
      if (!line.startsWith("SHARED_SECRET=")) {
        dev_vars.push(line);
      }
    });
  }
  fs2.writeFileSync(SECRET_PATH, dev_vars.join("\n"));
  return random_string;
}
async function uploadSecret(secret2) {
  await runWithStdin("npx", ["wrangler", "secret", "put", "SHARED_SECRET"], secret2);
}
async function secret(command) {
  if (command === "generate") {
    console.log(`Generating shared secret...`);
    generateSecret();
    console.log(chalk2.yellow(`Wrote SHARED_SECRET to .dev.vars`));
  } else if (command === "upload") {
    const secret2 = readSharedSecret();
    if (!secret2) {
      return console.log(
        [
          chalk2.red(`SHARED_SECRET not found in .dev.vars.`),
          `Run ${chalk2.yellow("npx workers-mcp secret generate")} to create one`
        ].join("\n")
      );
    }
    console.log(`Found secret. Running ${chalk2.yellow("wrangler secret put SHARED_SECRET")}`);
    await uploadSecret(secret2);
    console.log(chalk2.green(`Done!`));
  } else {
    console.log(`Unknown command: ${command}`);
  }
}

// src/scripts/install-claude.ts
import path2 from "node:path";
import os from "node:os";
import fs3 from "node:fs";
import chalk3 from "chalk";
import npmWhich from "npm-which";
async function installClaude(claude_name, workers_url) {
  if (!claude_name || !workers_url) {
    console.error("usage: npx workers-mcp install:claude <claude_name> <workers_url>");
    process.exit(1);
  }
  const claudeConfigPath = path2.join(
    os.homedir(),
    "Library",
    "Application Support",
    "Claude",
    "claude_desktop_config.json"
  );
  const mcpConfig = {
    command: npmWhich(process.cwd()).sync("workers-mcp"),
    args: ["run", claude_name, workers_url, process.cwd()],
    env: process.env.NODE_EXTRA_CA_CERTS ? { NODE_EXTRA_CA_CERTS: process.env.NODE_EXTRA_CA_CERTS } : {}
  };
  console.log(`Looking for existing config in: ${chalk3.yellow(path2.dirname(claudeConfigPath))}`);
  const configDirExists = isDirectory(path2.dirname(claudeConfigPath));
  if (configDirExists) {
    const existingConfig = fs3.existsSync(claudeConfigPath) ? JSON.parse(fs3.readFileSync(claudeConfigPath, "utf8")) : { mcpServers: {} };
    const newConfig = {
      ...existingConfig,
      mcpServers: {
        ...existingConfig.mcpServers,
        [claude_name]: mcpConfig
      }
    };
    fs3.writeFileSync(claudeConfigPath, JSON.stringify(newConfig, null, 2));
    console.log(`${chalk3.yellow(claude_name)} configured & added to Claude Desktop!`);
    console.log(`Wrote config to ${chalk3.yellow(claudeConfigPath)}:`);
    const redactedNewConfig = {
      ...Object.fromEntries(Object.keys(existingConfig).map((k) => [k, "..."])),
      mcpServers: {
        ...Object.fromEntries(Object.keys(existingConfig.mcpServers).map((k) => [k, "..."])),
        [claude_name]: mcpConfig
      }
    };
    console.log(chalk3.gray(JSON.stringify(redactedNewConfig, null, 2)));
  } else {
    const fullConfig = { mcpServers: { [claude_name]: mcpConfig } };
    console.log(
      `Couldn't detect Claude Desktop config at ${claudeConfigPath}.
To add the Cloudflare MCP server manually, add the following config to your ${chalk3.yellow("claude_desktop_configs.json")} file:

${JSON.stringify(fullConfig, null, 2)}`
    );
  }
}
function isDirectory(configPath) {
  try {
    return fs3.statSync(configPath).isDirectory();
  } catch (error) {
    return false;
  }
}

// src/scripts/local-proxy.ts
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { CallToolRequestSchema, ListToolsRequestSchema } from "@modelcontextprotocol/sdk/types.js";
import fs4 from "node:fs";
import path3 from "node:path";
import photon from "@silvia-odwyer/photon-node";
import { dir } from "tmp-promise";
function log(...args2) {
  const msg = `[DEBUG ${(/* @__PURE__ */ new Date()).toISOString()}] ${args2.join(" ")}
`;
  process.stderr.write(msg);
}
function reencodeImage(original) {
  const image = photon.PhotonImage.new_from_base64(original);
  return image.get_bytes_jpeg(80);
}
async function localProxy(claude_name, workers_url, workers_dir = process.cwd()) {
  if (!claude_name || !workers_url) {
    console.error("usage: npx workers-mcp run <claude_name> <workers_url> [workers_dir]");
    process.exit(1);
  }
  const tools_path = path3.resolve(workers_dir, "dist/docs.json");
  if (!fs4.existsSync(tools_path)) {
    console.error(`Could not find ${tools_path}`);
    process.exit(1);
  }
  const TOOLS = JSON.parse(fs4.readFileSync(tools_path, "utf-8"));
  log(JSON.stringify(TOOLS, null, 2));
  const secret_path = path3.join(workers_dir, ".dev.vars");
  if (!fs4.existsSync(secret_path)) {
    console.error(`Could not find ${secret_path}`);
    process.exit(1);
  }
  const SHARED_SECRET = fs4.readFileSync(secret_path, "utf8").split("\n").map((line) => {
    const match = line.match(/SHARED_SECRET=(.*)/);
    return match?.[1];
  }).find(Boolean);
  if (!SHARED_SECRET) {
    console.error(`Could not find SHARED_SECRET in ${secret_path}`);
    process.exit(1);
  }
  const { path: tmpdir } = await dir();
  log(`Using tmpdir: ${tmpdir}`);
  const server = new Server(
    { name: claude_name, version: "1.0.0" },
    { capabilities: {
      /*resources: {}, */
      tools: {}
    } }
  );
  const WORKER_SCHEMA = Object.values(TOOLS).find(
    (tool) => tool.exported_as === "default"
  );
  if (!WORKER_SCHEMA) {
    console.log(`No default exported WorkerEntrypoint found! Check dist/docs.json`);
    process.exit(1);
  }
  server.setRequestHandler(ListToolsRequestSchema, async () => {
    log("Received list tools request");
    return {
      tools: WORKER_SCHEMA.methods.map((doc) => {
        return {
          name: doc.name,
          description: doc.description,
          inputSchema: {
            type: "object",
            properties: Object.fromEntries(
              doc.params.map(({ name, description, type }) => [name, { description, type }])
            ),
            required: doc.params.map(({ name, optional }) => optional ? void 0 : name).filter(Boolean)
          }
        };
      })
    };
  });
  server.setRequestHandler(CallToolRequestSchema, async (request) => {
    const toolName = request.params.name;
    log("Received tool call:", toolName);
    const method = WORKER_SCHEMA.methods.find((doc) => doc.name === toolName);
    if (!method) {
      return {
        content: [{ type: "text", text: `Couldn't find method '${toolName}' in entrypoint` }],
        isError: true
      };
    }
    log(JSON.stringify(request.params));
    log(JSON.stringify(method.params));
    const args2 = method.params.map((param) => request.params.arguments?.[param.name]);
    const init = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + SHARED_SECRET
      },
      body: JSON.stringify({ method: toolName, args: args2 })
    };
    log(JSON.stringify(init));
    const response = await fetch(workers_url + "/rpc", init);
    const bytes = await response.arrayBuffer();
    if (bytes.byteLength === 0) {
      return {
        content: [{ type: "text", text: `Fetch failed. Got (${response.status}) Empty response` }],
        isError: true
      };
    }
    log(`Got ${bytes.byteLength} bytes`);
    const text2 = new TextDecoder().decode(bytes);
    if (!response.ok) {
      return {
        content: [{ type: "text", text: `Fetch failed. Got (${response.status}) ${text2}` }],
        isError: true
      };
    }
    const contentType = response.headers.get("content-type");
    const imageType = contentType?.match(/image\/(\w+)/);
    if (contentType?.match(/text\/plain/)) {
      return {
        content: [{ type: "text", text: text2 }]
      };
    } else if (imageType) {
      const buffer = Buffer.from(bytes);
      const type = imageType[1];
      const filename = path3.join(tmpdir, `${+/* @__PURE__ */ new Date()}.${type}`);
      fs4.writeFileSync(filename, buffer);
      let base64 = buffer.toString("base64");
      fs4.writeFileSync(filename + ".base64", base64);
      if (type === "jpeg") {
        const smallerImage = reencodeImage(base64);
        const smallerFile = `${filename}.reencode.${type}`;
        fs4.writeFileSync(smallerFile, smallerImage);
        base64 = Buffer.from(smallerImage).toString("base64");
        fs4.writeFileSync(smallerFile + ".base64", base64);
      }
      log(filename, contentType);
      return {
        content: [{ type: "image", data: base64, mimeType: contentType }]
      };
    } else if (contentType?.match(/application\/json/)) {
      const content = JSON.parse(text2);
      log(`Got response: ${text2.slice(0, 1e3)}`);
      return "content" in content ? content : {
        content: Array.isArray(content) ? content : [content]
      };
    } else {
      return {
        content: [{ type: "text", text: `Unknown contentType ${contentType} ${text2.slice(0, 1e3)}` }],
        isError: true
      };
    }
  });
  const transport = new StdioServerTransport();
  await server.connect(transport);
}

// src/scripts/help.ts
import chalk4 from "chalk";
var DELIM = chalk4.blue(new Array(80).fill("=").join(""));
async function help() {
  console.log(`
\u{1F4AA} Congratulations on installing ${chalk4.green("workers-mcp")} \u{1F60E}

${DELIM}
Note: the below instructions are for manual installation.
Run ${chalk4.green("npx workers install")} for a guided installation that automates this process.
${DELIM}

For manual installations, do the following 4 steps:

${chalk4.underline.green(`Step 1`)}
Add ${chalk4.yellow("workers-mcp docgen src/index.ts")} as part of your 'wrangler deploy' step, e.g.
${chalk4.gray(`
  "scripts": {
    "deploy:worker": "workers-mcp docgen src/index.ts && wrangler deploy"
  }
`)}
${chalk4.underline.green(`Step 2`)}
Within your Worker, add ${chalk4.yellow("ProxyToSelf")} to your .fetch handler:
${chalk4.gray(`
  import { ProxyToSelf } from 'workers-mcp'
  
  class MyWorker extends WorkerEntrypoint {
    // rpc methods here
    
    async fetch(request: Request): Promise<Response> {
      return new ProxyToSelf(this).fetch(request)
    }
  }
`)}
${chalk4.underline.green(`Step 3`)}
Generate a new shared secret and do the first-deployment dance:

\u2022 ${chalk4.yellow("npx workers-mcp secret generate")}
\u2022 ${chalk4.yellow("npm run deploy")}
\u2022 ${chalk4.yellow("npx workers-mcp secret upload")}
\u2022 (optional) ${chalk4.yellow("npx wrangler types")}

${chalk4.underline.green(`Step 4`)}
Install it, choosing a new name & using the URL from your first deployment above:
${chalk4.yellow("npx workers-mcp install:claude <name-within-claude> <url-to-your-hosted-worker>")}

\u{1F389} You're done! Now start up Claude Desktop and get prompting!

${DELIM}
Note: the above instructions are for manual installation.
Run ${chalk4.green("npx workers setup")} for a guided installation that automates this process.
${DELIM}`);
}

// src/scripts/setup.ts
import { confirm, intro, log as log2, outro, select, text } from "@clack/prompts";
import chalk5 from "chalk";
import fs5 from "fs-extra";
import path4 from "path";
import { scheduler } from "node:timers/promises";
var DELAY = 200;
async function guidedInstallation() {
  console.log(`
\u{1F4AA} Congratulations on installing ${chalk5.green("workers-mcp")} \u{1F60E}
`);
  intro(`Let's get started...`);
  await scheduler.wait(DELAY);
  try {
    const index_script = await determineIndexScript();
    await scheduler.wait(DELAY);
    const script_name = await addDocgen(index_script);
    await scheduler.wait(DELAY);
    await generateAndUploadSecret();
    await scheduler.wait(DELAY);
    await replaceSource(index_script);
    const url = await doDeploy(index_script, script_name);
    await scheduler.wait(DELAY);
    await install(url);
    outro(`\u{1F919} All done!`);
  } catch (e) {
    log2.error(chalk5.red("ERROR") + " " + e.message);
    process.exit(1);
  }
}
async function determineIndexScript() {
  const paths = ["src/index.ts", "src/index.js"];
  const index_script = paths.find(fs5.pathExistsSync);
  if (!index_script) {
    throw new Error(`Could not find a valid worker entrypoint file. Checked ${paths.join(",")}`);
  }
  return index_script;
}
async function addDocgen(index_script) {
  log2.info(
    `${chalk5.bold("Step 1")}: adding ${chalk5.yellow(`'workers-mcp docgen ${index_script}'`)} as part of your ${chalk5.yellow("wrangler deploy")} step`
  );
  const package_json_path = path4.resolve(process.cwd(), "package.json");
  if (!fs5.existsSync(package_json_path)) {
    throw new Error(`Could not find ${chalk5.yellow(package_json_path)}. Are you running in the right directory?`);
  }
  const package_json = await fs5.readJSON(package_json_path);
  const { scripts = {} } = package_json;
  const auto_choice = Object.entries(scripts).find(
    ([key, val]) => key.match(/deploy|publish|release/) && val.match(/wrangler deploy/)
  );
  if (auto_choice) {
    const [key, value] = auto_choice;
    if (value.match(/workers-mcp docgen/)) {
      log2.success(
        `NPM script ${chalk5.green(key)} already contains ${chalk5.yellow(`'workers-mcp docgen'`)}.
Keeping existing value:

  ${chalk5.grey(`"${key}": "${value}"`)}`
      );
      return;
    }
    const new_value = `workers-mcp docgen ${index_script} && ${value}`;
    log2.step(
      [
        `Found NPM script key ${chalk5.green(key)} that contains ${chalk5.yellow(`'wrangler deploy'`)}. Applying update:`,
        "",
        chalk5.red(`--- "${key}": "${value}"`),
        chalk5.green(`+++ "${key}": "${new_value}"`)
      ].join("\n")
    );
    if (await confirm({ message: "Proceed?" })) {
      scripts[key] = new_value;
      await fs5.writeJSON(package_json_path, package_json, { spaces: 2 });
      log2.success(`Updated package.json!`);
      return key;
    } else {
      log2.warn(`Skipping! You should add ${chalk5.yellow(`'workers-mcp docgen ${index_script}`)} yourself manually.`);
    }
  } else {
    log2.step(`Found the following NPM scripts:
${chalk5.grey(JSON.stringify(scripts, null, 2))}`);
    const choice = await select({
      message: `Which script would you like to prepend with ${chalk5.yellow(`workers-mcp docgen ${index_script} &&`)}?`,
      options: Object.keys(scripts).map((key) => ({ value: key, label: `"${key}"` })).concat({ value: "", label: "<skip>" })
    });
    if (choice === "" || typeof choice !== "string") {
      log2.warn(`Skipping! You should add ${chalk5.yellow(`'workers-mcp docgen ${index_script}'`)} yourself manually.`);
    } else {
      const value = scripts[choice];
      const new_value = `workers-mcp docgen ${index_script} && ${value}`;
      log2.step(
        [
          `Applying update:`,
          chalk5.red(`--- "${choice}": "${value}"`),
          chalk5.green(`+++ "${choice}": "${new_value}"`)
        ].join("\n")
      );
      scripts[choice] = new_value;
      await fs5.writeJSON(package_json_path, package_json, { spaces: 2 });
      log2.success(`Updated package.json!`);
      return choice;
    }
  }
}
async function generateAndUploadSecret() {
  log2.info(`${chalk5.bold("Step 2")}: generating and uploading a shared secret`);
  let do_upload = true;
  let secret2 = null;
  const dev_vars_exists = await fs5.pathExists(SECRET_PATH);
  if (dev_vars_exists) {
    const existing_secret = readSharedSecret();
    if (existing_secret?.length === 64) {
      log2.success(`SHARED_SECRET already present in ${chalk5.yellow(SECRET_PATH)}. It may already be uploaded.`);
      log2.warn(`To reupload it, run ${chalk5.yellow("npx workers-mcp secret upload")} manually.`);
      secret2 = existing_secret;
      do_upload = false;
    } else {
      log2.warn(`SHARED_SECRET present in ${chalk5.yellow(SECRET_PATH)} but appears invalid.`);
      if (await confirm({
        message: `Do you want to regenerate it?`
      })) {
        secret2 = generateSecret();
        log2.success(`Generated and stored SHARED_SECRET in .dev.vars`);
      } else {
        log2.warn(
          `Ok, skipping secret generation & upload!
Run ${chalk5.yellow("npx workers-mcp secret upload")} manually if required.`
        );
        do_upload = false;
      }
    }
  } else {
    secret2 = generateSecret();
    log2.success(`Generated and stored SHARED_SECRET in ${chalk5.yellow(".dev.vars")}`);
  }
  if (do_upload && secret2) {
    log2.step(`Uploading shared secret using ${chalk5.yellow("wrangler secret put")}:`);
    await uploadSecret(secret2);
    console.log("\n");
    if (await fs5.pathExists("worker-configuration.d.ts")) {
      log2.step(`Secret uploaded! Regenerating ${chalk5.yellow("worker-configuration.d.ts")}:`);
      await runCommand("npx", ["wrangler", "types"]);
    }
    log2.success(`Done!`);
  }
}
async function replaceSource(index_script) {
  log2.info(
    [
      `${chalk5.bold("Step 3")}: applying changes to ${chalk5.yellow(index_script)}`,
      chalk5.gray(
        `${chalk5.yellow("workers-mcp")} requires exporting a WorkerEntrypoint with a ${chalk5.yellow("ProxyToSelf")} or ${chalk5.yellow("ProxyToDO")} helper with each method annotated using JSDoc.`
      )
    ].join("\n")
  );
  const example_script = index_script.endsWith(`.ts`) ? EXAMPLE_TS : EXAMPLE_JS;
  log2.step(`For example, a "hello world" MCP Worker looks like
${chalk5.gray(example_script)}`);
  const source = fs5.readFileSync(index_script, "utf8");
  const imports_a_proxy_method = source.match(/import.*Proxy.*workers-mcp/);
  const exports_an_entrypoint = source.match(/export default.*extends WorkerEntrypoint/);
  if (imports_a_proxy_method && exports_an_entrypoint) {
    log2.success(`Your ${chalk5.yellow(index_script)} seems to be already set up! Skipping...`);
  } else {
    if (await confirm({
      message: `Would you like to replace the contents of ${chalk5.yellow(index_script)} with the above example code?`
    })) {
      await fs5.writeFile(index_script, example_script);
      log2.success(`Success! ${chalk5.yellow(index_script)} written.`);
    }
  }
}
async function doDeploy(index_script, script_name) {
  log2.info([`${chalk5.bold("Step 4")}: deploying your code!`].join("\n"));
  const stdout = await (script_name ? runCommand("npm", ["run", script_name]) : generateDocs(index_script).then(() => runCommand("npx", ["wrangler", "deploy"])));
  const url = stdout.split("\n").map((line) => line.match(/(https:\/\/.*)/)).find(Boolean)?.[1];
  if (!url) {
    throw new Error(
      `Unable to determine which URL your worker was deployed to.
Please run ${chalk5.yellow("npx workers-mcp install:claude <name-within-claude> <url-to-your-hosted-worker>")} manually.`
    );
  }
  log2.success(`Success! Worker deployed to ${chalk5.yellow(url)}`);
  return url;
}
async function install(url) {
  log2.info(`${chalk5.bold("Step 5")}: installing on Claude Desktop`);
  const claude_name = await text({
    message: "What name should we use for this worker within Claude?",
    initialValue: path4.basename(process.cwd())
  });
  if (claude_name === "") {
    log2.warn(
      `Skipping! Please run ${chalk5.yellow("npx workers-mcp install:claude <name-within-claude> <url-to-your-hosted-worker>")} manually.`
    );
  } else {
    await installClaude(claude_name, url);
  }
}

// src/cli.ts
function log3(...args2) {
  const msg = `[DEBUG ${(/* @__PURE__ */ new Date()).toISOString()}] ${args2.join(" ")}
`;
  process.stderr.write(msg);
}
process.on("uncaughtException", (error) => {
  log3("Uncaught exception:", error);
});
process.on("unhandledRejection", (error) => {
  log3("Unhandled rejection:", error);
});
var [cmd, ...args] = process.argv.slice(2);
if (cmd === "docgen") {
  await generateDocs(args[0]);
} else if (cmd === "setup") {
  await guidedInstallation();
} else if (cmd === "secret") {
  await secret(args[0]);
} else if (cmd === "install:claude") {
  await installClaude(args[0], args[1]);
} else if (cmd === "run") {
  await localProxy(args[0], args[1], args[2]);
} else if (cmd === "help") {
  await help();
} else {
  console.log(`Unknown command: ${cmd}`);
}
export {
  log3 as log
};
