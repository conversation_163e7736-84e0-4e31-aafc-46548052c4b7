{"name": "ts-blank-space", "description": "A small, fast, pure JavaScript type-stripper that uses the official TypeScript parser.", "version": "0.4.4", "license": "Apache-2.0", "homepage": "https://bloomberg.github.io/ts-blank-space", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON>-Drago<PERSON> <<EMAIL>>"], "type": "module", "main": "./out/index.js", "exports": {".": "./out/index.js", "./package.json": "./package.json", "./register": "./loader/register.js"}, "engines": {"node": ">=18.0.0"}, "types": "./out/index.d.ts", "dependencies": {"typescript": "5.1.6 - 5.7.x"}, "imports": {"#r": "ts-blank-space-lkg/register"}, "devDependencies": {"@types/node": "^20.9.4", "prettier": "3.3.3", "ts-blank-space-lkg": "npm:ts-blank-space@^0.3.3333"}, "scripts": {"build": "node --import=#r ./scripts/build.ts", "check": "tsc -p ./src/tsconfig.json --noEmit", "gen-types": "tsc -p ./src/tsconfig.json --emitDeclarationOnly", "fixtures": "node --import=#r ./tests/fixture/_run.ts", "fixtures:watch": "node --watch-path ./tests/fixture/cases --import=#r ./tests/fixture/_run.ts", "pretest": "npm run build", "test": "node --test --import=#r ./tests/*.test.ts", "test-ecosystem": "node --test --import=#r ./tests/ecosystem/ecosystem.test.ts", "format": "prettier . -w", "format:check": "prettier . -c", "prepack": "npm run gen-types;npm run build;"}, "files": ["!tests", "!perf", "out/*.js", "out/*.d.ts", "loader/*.js", "loader/*.d.ts"], "repository": {"type": "git", "url": "git+https://github.com/bloomberg/ts-blank-space.git"}}