/** Like magic-string but with only one feature */
export default class BlankString {
    __input: string;
    __ranges: number[];
    constructor(input: string);
    blankButStartWithOpenParen(start: number, end: number): void;
    blankButEndWithCloseParen(start: number, end: number): void;
    blankButStartWithSemi(start: number, end: number): void;
    blank(start: number, end: number): void;
    toString(): string;
}
