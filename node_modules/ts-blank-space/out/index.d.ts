import type * as ts from "typescript";
type ErrorCb = (n: ts.Node) => void;
/**
 * @param input string containing TypeScript
 * @param onErrorArg callback when unsupported syntax is encountered
 * @returns the resulting JavaScript
 */
export default function tsBlankSpace(input: string, onErrorArg?: ErrorCb): string;
/**
 * @param source containing TypeScript's AST
 * @param onErrorArg callback when unsupported syntax is encountered
 * @returns the resulting JavaScript
 */
export declare function blankSourceFile(source: ts.SourceFile, onErrorArg?: ErrorCb): string;
export {};
