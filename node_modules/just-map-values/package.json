{"name": "just-map-values", "version": "3.2.0", "description": "map an object, predicate updates values, receives (value, key, object)", "type": "module", "exports": {".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.mjs"}, "./package.json": "./package.json"}, "main": "index.cjs", "types": "index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "rollup -c"}, "repository": "https://github.com/angus-c/just", "keywords": ["object", "map", "values", "no-dependencies", "just"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/angus-c/just/issues"}}