{"clientTcpRtt": 130, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 4812, "clientAcceptEncoding": "br, gzip, deflate", "verifiedBotCategory": "", "country": "CN", "isEUCountry": false, "region": "Shanghai", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "vJ/88Vl/jI6cShftimb9J2SzbdyqukftGlKB2dS6/FQ=", "tlsExportedAuthenticator": {"clientFinished": "980eb9a6fd97cfe1edf857f2bd8411b2b774e159904609f885a0eef0def14f27efda182748e72371effede934f2ce5d5", "clientHandshake": "b6d07a4c18f45a4747483a0b0be3a1fcdad7356ca36849b60398b446e7c46b51fbde54471717b1aef6df123c661f6ac5", "serverHandshake": "7e492c5f7470052d04fe0bb94cf38e0ea622b61d74a56816f03cbd99d0b20e745b4b2da2687153a0d2a2330eea8ea782", "serverFinished": "1f2f3fcbc59a0194def5264257deea67abe2b7ad63af404ea612ea7e593c5ec08ace6a803d7382f58d270dd3fbab6cef"}, "tlsClientHelloLength": "386", "colo": "SJC", "timezone": "Asia/Shanghai", "longitude": "121.45806", "latitude": "31.22222", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "200000", "city": "Shanghai", "tlsVersion": "TLSv1.3", "regionCode": "SH", "asOrganization": "CHINANET SHANGHAI PROVINCE NETWORK", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}