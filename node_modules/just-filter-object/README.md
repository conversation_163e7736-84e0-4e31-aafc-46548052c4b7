<!-- DO NOT EDIT THIS FILE! THIS FILE WAS AUTOGENERATED BY TEMPLATE-MATE -->
<!-- SEE https://github.com/angus-c/just/blob/master/CONTRIBUTING.md#readme-template -->

## just-filter-object

Part of a [library](https://anguscroll.com/just) of zero-dependency npm modules that do just do one thing.
Guilt-free utilities for every occasion.

[`🍦 Try it`](https://anguscroll.com/just/just-filter-object)

```shell
npm install just-filter-object
```
```shell
yarn add just-filter-object
```

Filter an object

```js
import filter from 'just-filter';

// returns a new object containing those original properties for which the predicate returns truthy
filter({a: 3, b: 5, c: 9}, (key, value) => value < 6); // {a: 3, b: 5}
filter({a1: 3, b1: 5, a2: 9}, (key, value) => key[0] == 'a'); // {a1: 3, a2: 9}
filter({a: 3, b: 5, c: null}, (key, value) => value); // {a: 3, b: 5}
```
