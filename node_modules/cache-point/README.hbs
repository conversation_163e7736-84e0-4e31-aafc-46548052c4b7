[![view on npm](https://badgen.net/npm/v/cache-point)](https://www.npmjs.org/package/cache-point)
[![npm module downloads](https://badgen.net/npm/dt/cache-point)](https://www.npmjs.org/package/cache-point)
[![Gihub repo dependents](https://badgen.net/github/dependents-repo/75lb/cache-point)](https://github.com/75lb/cache-point/network/dependents?dependent_type=REPOSITORY)
[![Gihub package dependents](https://badgen.net/github/dependents-pkg/75lb/cache-point)](https://github.com/75lb/cache-point/network/dependents?dependent_type=PACKAGE)
[![Node.js CI](https://github.com/75lb/cache-point/actions/workflows/node.js.yml/badge.svg)](https://github.com/75lb/cache-point/actions/workflows/node.js.yml)
[![js-standard-style](https://img.shields.io/badge/code%20style-standard-brightgreen.svg)](https://github.com/feross/standard)

# cache-point

Simple, filesystem-backed memoisation cache. Use to cache the output of expensive operations speeding up future invocations with the same input.

## Synopsis

```js
import Cache from 'cache-point'
import { setTimeout as sleep } from 'node:timers/promises'

/* mock function to simulate a slow, remote request */
async function fetchUser (id) {
  await sleep(1000)
  return { id, name: 'Layla' }
}

class Users {
  constructor () {
    this.cache = new Cache({ dir: 'tmp/example' })
  }

  async getUser (id) {
    let user
    try {
      /* cache.read() will resolve on hit, reject on miss */
      user = await this.cache.read(id)
    } catch (err) {
      if (err.code === 'ENOENT') {
        /* cache miss, fetch remote user */
        user = await fetchUser(id)
        this.cache.write(id, user)
      }
    }
    return user
  }
}

console.time('getUser')
const users = new Users()
const user = await users.getUser(10)
console.timeEnd('getUser')
console.log(user)
```

The first invocation will take 1 second while the remote user is fetched.

```
$ node example/simple.js
getUser: 1.025s
{ id: 10, name: 'Layla' }
```

Since the cache is now warm, future invocations will be fast.

```
$ node example/simple.js
getUser: 17.07ms
{ id: 10, name: 'Layla' }
```

## API Reference

{{>main}}

* * *

&copy; 2016-25 Lloyd Brookes \<<EMAIL>\>.

Tested by [test-runner](https://github.com/test-runner-js/test-runner). Documented by [jsdoc-to-markdown](https://github.com/jsdoc2md/jsdoc-to-markdown).
