{"name": "jsdoc-api", "author": "<PERSON> <<EMAIL>>", "version": "9.3.4", "description": "A programmatic interface for jsdoc", "repository": {"type": "git", "url": "git+https://github.com/jsdoc2md/jsdoc-api.git"}, "type": "module", "exports": {"import": "./index.js", "require": "./dist/index.cjs"}, "license": "MIT", "keywords": ["jsdoc", "api", "programmatic", "interface", "javascript", "documentation"], "engines": {"node": ">=12.17"}, "scripts": {"test": "npm run dist && npm run test:ci", "test:ci": "75lb-nature test-runner test/caching.js test/explain.js test/render.js", "dist": "75lb-nature cjs-build index.js", "docs": "75lb-nature jsdoc2md index.js lib/*.js > docs/api.md"}, "dependencies": {"array-back": "^6.2.2", "cache-point": "^3.0.0", "current-module-paths": "^1.1.2", "file-set": "^5.2.2", "jsdoc": "^4.0.4", "object-to-spawn-args": "^2.0.1", "walk-back": "^5.1.1"}, "peerDependencies": {"@75lb/nature": "latest"}, "peerDependenciesMeta": {"@75lb/nature": {"optional": true}}, "standard": {"ignore": ["tmp", "test/fixture", "dist"]}, "files": ["index.js", "lib", "dist"]}