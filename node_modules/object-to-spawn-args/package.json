{"name": "object-to-spawn-args", "version": "2.0.1", "description": "Converts an object to a child_process.spawn args array", "repository": "https://github.com/75lb/object-to-spawn-args", "scripts": {"test": "test-runner test.js"}, "keywords": ["child_process", "spawn"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"test-runner": "^0.6.3"}, "engines": {"node": ">=8.0.0"}, "files": ["index.js"]}