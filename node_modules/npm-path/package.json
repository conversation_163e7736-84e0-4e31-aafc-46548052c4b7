{"name": "npm-path", "version": "2.0.4", "description": "Get a PATH with all executables available to npm scripts.", "main": "index.js", "bin": {"npm-path": "bin/npm-path"}, "scripts": {"clean": "rimraf build-test && (rm *.js || true)", "prebuild": "npm run clean", "build": "babel src --out-dir . && babel test --out-dir build-test", "prepublish": "not-in-publish || npm run build", "tests-only": "tape build-test -r airbnb-js-shims | faucet", "pretest": "standard src/* test/* bin/*", "test": "npm run build && npm run tests-only"}, "author": "<PERSON>", "license": "MIT", "engines": {"node": ">=0.8"}, "devDependencies": {"airbnb-js-shims": "^1.4.0", "babel-cli": "^6.26.0", "babel-preset-airbnb": "^2.4.0", "babel-register": "^6.26.0", "faucet": "0.0.1", "in-publish": "^2.0.0", "npm": "^3.10.10", "rimraf": "^2.6.2", "standard": "^7.1.2", "tape": "^4.8.0"}, "dependencies": {"which": "^1.2.10"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "https://github.com/timoxley/npm-path.git"}, "keywords": ["npm", "run", "executable"], "bugs": {"url": "https://github.com/timoxley/npm-path/issues"}, "homepage": "https://github.com/timoxley/npm-path"}