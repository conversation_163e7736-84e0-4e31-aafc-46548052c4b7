[![view on npm](https://badgen.net/npm/v/walk-back)](https://www.npmjs.org/package/walk-back)
[![npm module downloads](https://badgen.net/npm/dt/walk-back)](https://www.npmjs.org/package/walk-back)
[![Gihub repo dependents](https://badgen.net/github/dependents-repo/75lb/walk-back)](https://github.com/75lb/walk-back/network/dependents?dependent_type=REPOSITORY)
[![Gihub package dependents](https://badgen.net/github/dependents-pkg/75lb/walk-back)](https://github.com/75lb/walk-back/network/dependents?dependent_type=PACKAGE)
[![Node.js CI](https://github.com/75lb/walk-back/actions/workflows/node.js.yml/badge.svg)](https://github.com/75lb/walk-back/actions/workflows/node.js.yml)
[![js-standard-style](https://img.shields.io/badge/code%20style-standard-brightgreen.svg)](https://github.com/feross/standard)

{{>main}}

* * *

&copy; 2015-24 Lloyd Brookes \<<EMAIL>\>.

Tested by [test-runner](https://github.com/test-runner-js/test-runner). Documented by [jsdoc-to-markdown](https://github.com/jsdoc2md/jsdoc-to-markdown).
