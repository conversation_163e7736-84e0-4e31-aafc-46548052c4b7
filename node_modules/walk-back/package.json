{"name": "walk-back", "author": "<PERSON> <<EMAIL>>", "version": "5.1.1", "description": "Walk up the directory tree until the specified path is found.", "repository": "https://github.com/75lb/walk-back", "license": "MIT", "type": "module", "exports": {"import": "./index.js", "require": "./dist/index.cjs"}, "keywords": ["walk", "up", "back", "filesystem", "fs", "find", "file", "search", "system"], "engines": {"node": ">=12.17"}, "scripts": {"test": "test-runner test/test.js test/test.cjs", "docs": "jsdoc2md -t README.hbs index.js > README.md"}, "devDependencies": {"jsdoc-to-markdown": "^8.0.3", "test-runner": "^0.12.0-1"}, "files": ["index.js", "dist"]}