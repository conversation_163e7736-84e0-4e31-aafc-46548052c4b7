var p=Object.defineProperty;var n=(t,s)=>p(t,"name",{value:s,configurable:!0});import{r as f}from"./get-pipe-path-BHW2eJdv.mjs";import{constants as l}from"node:os";import{isMainThread as a}from"node:worker_threads";import{c as m}from"./client-BQVF1NaW.mjs";import"./suppress-warnings.mjs";import"module";import"node:path";import"./temporary-directory-CwHp0_NW.mjs";import"node:net";const u=n((t,s)=>{for(const e of t)process.on(e,r=>{s(r),process.listenerCount(e)===0&&process.exit(128+l.signals[e])});const{listenerCount:i,listeners:o}=process;process.listenerCount=function(e){let r=Reflect.apply(i,this,arguments);return t.includes(e)&&(r-=1),r},process.listeners=function(e){const r=Reflect.apply(o,this,arguments);return t.includes(e)?r.filter(c=>c!==s):r}},"bindHiddenSignalsHandler");a&&(f("./cjs/index.cjs"),(async()=>{const t=await m;t&&u(["SIGINT","SIGTERM"],s=>{t({type:"signal",signal:s})})})());
