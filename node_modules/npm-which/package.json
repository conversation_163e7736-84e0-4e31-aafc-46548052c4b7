{"name": "npm-which", "version": "3.0.1", "description": "Locate a program or locally installed node module's executable", "main": "index.js", "bin": {"npm-which": "bin/npm-which.js"}, "scripts": {"test": "node test/index.js | tap-spec"}, "author": "<PERSON>", "license": "MIT", "engines": {"node": ">=4.2.0"}, "dependencies": {"commander": "^2.9.0", "npm-path": "^2.0.2", "which": "^1.2.10"}, "devDependencies": {"tap-spec": "^4.1.1", "tape": "^4.6.0"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "https://github.com/timoxley/npm-which.git"}, "keywords": ["npm", "path", "executable", "run"], "bugs": {"url": "https://github.com/timoxley/npm-which/issues"}, "homepage": "https://github.com/timoxley/npm-which"}